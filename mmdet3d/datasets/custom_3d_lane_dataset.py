import os
import json
import yaml
import numpy as np
from os import path as osp
from mmdet.datasets import DATASETS
from .custom_3d import Custom3DDataset
import mmcv
import tempfile
import torch
from mmcv.utils import print_log
from pyquaternion import Quaternion
import torch.nn.functional as F

@DATASETS.register_module()
class Custom3DLaneDataset(Custom3DDataset):
    """Custom Dataset for 3D Lane Detection.

    Args:
        dataset_root (str): Path of dataset root.
        ann_file (str): Path of annotation file.
        pipeline (list[dict], optional): Pipeline used for data processing.
            Defaults to None.
        lane_classes (tuple[str], optional): Lane classes used in the dataset.
            Defaults to None.
        modality (dict, optional): Modality to specify the sensor data used
            as input. Defaults to None.
        box_type_3d (str, optional): Type of 3D box of this dataset.
            Defaults to 'LiDAR' in this dataset. Available options includes
            - 'LiDAR': box in LiDAR coordinates
            - 'Depth': box in depth coordinates, usually for indoor dataset
            - 'Camera': box in camera coordinates
        filter_empty_gt (bool, optional): Whether to filter empty GT.
            Defaults to True.
        test_mode (bool, optional): Whether the dataset is in test mode.
            Defaults to False.
        load_interval (int, optional): Interval of loading the dataset. It is
            used to uniformly sample the dataset. Defaults to 1.
        cam_list (list[str], optional): List of camera names used in the dataset.
            Defaults to None.
        point_cloud_range (list[float], optional): Range of point cloud coordinates.
            Defaults to None.
        eval_version (str, optional): Version for evaluation. Defaults to 'OpenLane'.
    """

    def __init__(self,
                 dataset_root,
                 ann_file,
                 pipeline=None,
                 lane_classes=None,
                 modality=None,
                 box_type_3d='LiDAR',
                 filter_empty_gt=True,
                 test_mode=False,
                 load_interval=1,
                 cam_list=None,
                 point_cloud_range=None,
                 eval_version='OpenLane'):
        """Initialize Custom3DLaneDataset.

        Args:
            dataset_root (str): Root path of dataset.
            ann_file (str): Annotation file path.
            pipeline (list[dict]): Processing pipeline.
            lane_classes (tuple[str]): List of lane classes.
            modality (dict): Modality to specify the sensor data used.
            box_type_3d (str): Type of 3D box (LiDAR, Camera, Depth).
            filter_empty_gt (bool): Whether to filter empty ground truths.
            test_mode (bool): Whether in test mode.
            load_interval (int): Load data interval.
            cam_list (list[str]): List of camera names.
            point_cloud_range (list[float]): Point cloud range.
            eval_version (str): Evaluation version.
        """
        self.dataset_root = dataset_root
        self.load_interval = load_interval
        
        # 设置默认相机列表，如果未提供
        if cam_list is None:
            self.cam_list = ['60_front', '120_front', '120_left', '120_right', 
                            '120_back', 'right_back', 'left_back']
        else:
            self.cam_list = cam_list
        
        print(f"[Custom3DLaneDataset] Initialized with {len(self.cam_list)} cameras: {self.cam_list}")
        
        # 设置点云范围，如果未提供
        if point_cloud_range is None:
            self.point_cloud_range = [-81.6, -48, -1, 97.6, 48, 3.0]
        else:
            self.point_cloud_range = point_cloud_range
        
        # 设置车道线类别
        self.LANE_CLASSES = lane_classes if lane_classes else []
        self.lane2id = {name: i for i, name in enumerate(self.LANE_CLASSES)}
        
        # 初始化使用基类的CLASSES属性作为LANE_CLASSES
        self.CLASSES = self.LANE_CLASSES
        
        # 初始化基类
        super().__init__(
            dataset_root=dataset_root,
            ann_file=ann_file,
            pipeline=pipeline,
            classes=self.LANE_CLASSES,  # 直接使用lane_classes作为classes
            modality=modality,
            box_type_3d=box_type_3d,
            filter_empty_gt=filter_empty_gt,
            test_mode=test_mode,
            point_cloud_range=point_cloud_range,
        )
        
        # 设置默认模态
        if self.modality is None:
            self.modality = dict(
                use_camera=True,
                use_lidar=True,
                use_radar=False,
                use_map=False,
                use_external=False,
            )
        
        # 设置评估版本
        self.eval_version = eval_version

    def get_data_info(self, index):
        """Get data info according to the given index."""
        info = self.data_infos[index]
        sample_idx = info.get('frame_id', str(index))
        
        # 基本数据字段
        input_dict = dict(
            sample_idx=sample_idx,
            timestamp=info.get('timestamp', ''),
            segment_id=info.get('segment_id', '')
        )
        
        # Lidar path
        if 'lidar_path' in info and info['lidar_path']:
            lidar_path = info['lidar_path']
            if not osp.isabs(lidar_path):
                lidar_path = osp.join(self.dataset_root, lidar_path)
            input_dict['lidar_path'] = lidar_path
        else:
            # 构建lidar路径
            segment_path = osp.join(self.dataset_root, info.get('segment_id', ''))
            lidar_path = osp.join(segment_path, 'lidar', f"{sample_idx}.pcd")
            input_dict['lidar_path'] = lidar_path if osp.exists(lidar_path) else None
        
        # 处理相机路径和标定数据
        if self.cam_list and 'cam_paths' in info:
            self._process_camera_data(input_dict, info)
        
        # 处理标注信息
        if not self.test_mode and 'annos' in info and info['annos']:
            annos = self.get_ann_info(index)
            input_dict['ann_info'] = annos
            
            if self.filter_empty_gt:
                has_gt_lanes = len(annos['gt_lanes_3d']) > 0
                if not has_gt_lanes:
                    return None
        
        return input_dict

    def _process_camera_data(self, input_dict, info):
        """处理相机数据和标定信息
        
        适配MogoB2Dataset风格，支持7个相机: ['60_front', '120_front', '120_left', '120_right', '120_back', 'right_back', 'left_back']
        保持对'120_front'相机的特殊处理以加载深度图
        """
        # 初始化输出数组
        input_dict['lidar2camera'] = []
        input_dict['camera_intrinsics'] = []
        input_dict['image_paths'] = []
        
        # 加载标定文件 - 参考MogoB2Dataset的标定加载方式
        calib_data = {}
        calib_path = info.get('calib_path')
        if calib_path:
            # FIXED: Construct absolute path by combining with dataset_root
            if not osp.isabs(calib_path):
                full_calib_path = osp.join(self.dataset_root, calib_path)
            else:
                full_calib_path = calib_path
            
            if osp.exists(full_calib_path):
                try:
                    with open(full_calib_path, 'r') as f:
                        calib_data = json.load(f)
                    print(f"[CALIB_SUCCESS] Loaded calibration data with {len(calib_data)} cameras")
                    # 转换标定数据格式以匹配MogoB2Dataset的处理方式
                    calib_data = self._update_vehicle_calib(calib_data, self.cam_list)
                except Exception as e:
                    print(f"[CALIB_ERROR] Error loading calibration data from {full_calib_path}: {e}")
                    calib_data = {}
            else:
                print(f"[CALIB_ERROR] Calibration file not found at: {full_calib_path}")
                calib_data = {}
        else:
            print(f"[CALIB_WARNING] No calibration path provided - using identity matrices")
        
        # 处理每个相机，按照cam_list顺序
        for cam in self.cam_list:
            cam_name = cam.replace('-', '_')
            
            # 处理相机路径 - 适配不同的路径格式
            cam_path = None
            if 'cam_paths' in info and cam_name in info['cam_paths'] and info['cam_paths'][cam_name]:
                cam_path = info['cam_paths'][cam_name]
                if not osp.isabs(cam_path):
                    cam_path = osp.join(self.dataset_root, cam_path)
                
                if osp.exists(cam_path):
                    input_dict['image_paths'].append(cam_path)
                else:
                    input_dict['image_paths'].append(None)
                    continue
            else:
                input_dict['image_paths'].append(None)
                continue
            
            # 相机内参和外参
            if cam in calib_data:
                cam_data = calib_data[cam]
                
                # 相机内参
                intrinsic = np.array(cam_data['intrinsic']).reshape(3, 3)
                cam_intrinsic = np.eye(4).astype(np.float32)
                cam_intrinsic[:3, :3] = intrinsic
                input_dict['camera_intrinsics'].append(cam_intrinsic)
                
                # LiDAR到相机的外参
                extrinsic = np.array(cam_data['extrinsic']).reshape(3, 4)
                lidar2cam = np.eye(4).astype(np.float32)
                lidar2cam[:3, :] = extrinsic
                input_dict['lidar2camera'].append(lidar2cam)
            else:
                # 使用单位矩阵
                print(f"[CALIB_WARNING] Camera {cam}: no calibration data found, using identity matrices")
                cam_intrinsic = np.eye(4).astype(np.float32)
                input_dict['camera_intrinsics'].append(cam_intrinsic)
                
                lidar2cam = np.eye(4).astype(np.float32)
                input_dict['lidar2camera'].append(lidar2cam)
        
        # 计算额外的变换矩阵
        if input_dict['lidar2camera'] and input_dict['camera_intrinsics']:
            # lidar2image变换
            input_dict['lidar2image'] = []
            for cam_idx in range(len(input_dict['camera_intrinsics'])):
                lidar2image = input_dict['camera_intrinsics'][cam_idx] @ input_dict['lidar2camera'][cam_idx]
                input_dict['lidar2image'].append(lidar2image)
            
            # 创建其他变换矩阵
            lidar2ego = np.eye(4).astype(np.float32)
            input_dict['lidar2ego'] = lidar2ego
            
            input_dict['camera2ego'] = []
            input_dict['camera2lidar'] = []
            for lidar2cam in input_dict['lidar2camera']:
                cam2lidar = np.linalg.inv(lidar2cam)
                input_dict['camera2lidar'].append(cam2lidar)
                
                cam2ego = lidar2ego @ cam2lidar
                input_dict['camera2ego'].append(cam2ego)

    def load_annotations(self, ann_file):
        """Load annotations from ann_file.

        Args:
            ann_file (str): Path of the annotation file.

        Returns:
            list[dict]: List of annotations sorted by timestamps.
        """
        # 处理pkl格式的标注文件 (train_annotations.pkl, val_annotations.pkl, test_info.pkl)
        if ann_file.endswith('.pkl'):
            try:
                data_infos = mmcv.load(ann_file)
                
                # 如果data_infos是dict类型，可能有额外的数据结构
                if isinstance(data_infos, dict):
                    if 'infos' in data_infos:
                        data_infos = data_infos['infos']
                    elif 'data_infos' in data_infos:
                        data_infos = data_infos['data_infos']
                    else:
                        print(f"Warning: Unexpected dictionary format. Keys: {list(data_infos.keys())}")
                
                # 过滤基于load_interval
                if self.load_interval > 1:
                    data_infos = data_infos[::self.load_interval]
                
                return data_infos
            except Exception as e:
                print(f"Error loading pickle file: {e}")
                import traceback
                traceback.print_exc()
                return []
        
        # 处理JSON格式的标注文件
        elif ann_file.endswith('.json'):
            try:
                with open(ann_file, 'r') as f:
                    data_dict = json.load(f)
                
                data_infos = []
                if isinstance(data_dict, list):
                    # 直接是列表格式
                    data_infos = data_dict
                elif isinstance(data_dict, dict) and 'frames' in data_dict:
                    # 包含frames字段的字典格式
                    data_infos = data_dict['frames']
                else:
                    # 其他格式，尝试转换
                    for key, value in data_dict.items():
                        if isinstance(value, dict):
                            frame_info = value.copy()
                            frame_info['frame_id'] = key
                            data_infos.append(frame_info)
                
                # 处理每个帧的信息
                processed_infos = []
                for frame_info in data_infos:
                    # 处理帧信息
                    info = {
                        'timestamp': frame_info.get('timestamp', ''),
                        'segment_id': frame_info.get('segment_id', ''),
                        'frame_id': frame_info.get('frame_id', ''),
                        'cam_paths': {},
                        'annos': frame_info.get('lane_lines', [])
                    }
                    
                    # 处理传感器信息
                    if 'sensors' in frame_info:
                        sensors = frame_info['sensors']
                        for sensor_name, sensor_data in sensors.items():
                            if sensor_name.startswith('cam_') and 'image_path' in sensor_data:
                                info['cam_paths'][sensor_name] = osp.join(self.dataset_root, sensor_data['image_path'])
                            elif sensor_name == 'lidar' and 'pointcloud_path' in sensor_data:
                                info['lidar_path'] = osp.join(self.dataset_root, sensor_data['pointcloud_path'])
                    
                    processed_infos.append(info)
                
                # 过滤基于load_interval
                if self.load_interval > 1:
                    processed_infos = processed_infos[::self.load_interval]
                
                return processed_infos
            except Exception as e:
                print(f"Error loading JSON file: {e}")
                return []
        
        # 处理包含JSON路径的索引文件
        else:
            try:
                with open(ann_file, 'r') as f:
                    lines = f.readlines()
                
                data_infos = []
                for idx, line in enumerate(lines[::self.load_interval]):
                    json_path = line.strip()
                    if not osp.isabs(json_path):
                        json_path = osp.join(self.dataset_root, json_path)
                    
                    try:
                        with open(json_path, 'r') as f:
                            frame_info = json.load(f)
                        
                        # 提取segment和frame（如果不在JSON中）
                        if 'segment_id' not in frame_info:
                            parts = json_path.split('/')
                            segment_idx = parts.index('annotations') - 1 if 'annotations' in parts else -2
                            if segment_idx >= 0:
                                frame_info['segment_id'] = parts[segment_idx]
                        
                        # 格式化与上面相同
                        info = {
                            'timestamp': frame_info.get('timestamp', str(idx).zfill(10)),
                            'segment_id': frame_info.get('segment_id', ''),
                            'frame_id': osp.splitext(osp.basename(json_path))[0],
                            'ann_path': json_path,
                            'cam_paths': {},
                            'annos': frame_info.get('lane_lines', [])
                        }
                        
                        data_infos.append(info)
                    except Exception as e:
                        print(f"Error loading JSON file {json_path}: {e}")
                
                return data_infos
            except Exception as e:
                print(f"Error loading index file: {e}")
                return []

    def get_ann_info(self, idx):
        """Get annotation info according to the given index.

        Args:
            idx (int): Index of the annotation data to get.

        Returns:
            dict: Annotation information consists of lane annotations.
        """
        info = self.data_infos[idx]
        gt_lanes_3d = []
        gt_lane_labels = []
        gt_lane_points_uvs = []
        
        # Analyze lane type distribution in this dataset
        lane_type_counts = {}
        
        if 'annos' in info:
            for lane_anno in info['annos']:
                # 处理3D车道线点坐标
                if 'xyz' in lane_anno and lane_anno['xyz']:
                    # 确保xyz是坐标点列表
                    if isinstance(lane_anno['xyz'][0], (list, tuple, np.ndarray)):
                        lane_points = np.array(lane_anno['xyz'], dtype=np.float32)
                    else:
                        # 如果xyz不是点列表，而是展平的坐标值，则需要重塑
                        # 假设格式为[x1, y1, z1, x2, y2, z2, ...]
                        coords = np.array(lane_anno['xyz'], dtype=np.float32)
                        if len(coords) % 3 == 0:
                            lane_points = coords.reshape(-1, 3)
                        else:
                            print(f"[ANNO_ERROR] Invalid xyz format in lane annotation: {len(coords)} values")
                            continue
                    
                    # 添加3D点
                    gt_lanes_3d.append(lane_points)
                    
                    # 处理车道线类型标签
                    lane_type = lane_anno.get('type_id', 0)
                    
                    # Enhanced debugging for lane type
                    if hasattr(self, 'lane_classes') and self.lane_classes is not None and lane_type < len(self.lane_classes):
                        lane_class_name = self.lane_classes[lane_type]
                    else:
                        lane_class_name = f"Unknown (id: {lane_type})"
                        
                    # Track lane type distribution
                    if lane_type in lane_type_counts:
                        lane_type_counts[lane_type] += 1
                    else:
                        lane_type_counts[lane_type] = 1
                    
                    gt_lane_labels.append(lane_type)
                    
                    # 处理2D UV坐标点（如果有）
                    if 'uv' in lane_anno and lane_anno['uv']:
                        # 确保uv是坐标点列表
                        if isinstance(lane_anno['uv'][0], (list, tuple, np.ndarray)):
                            uv_points = np.array(lane_anno['uv'], dtype=np.float32)
                        else:
                            # 如果uv不是点列表，而是展平的坐标值，则需要重塑
                            # 假设格式为[u1, v1, u2, v2, ...]
                            coords = np.array(lane_anno['uv'], dtype=np.float32)
                            if len(coords) % 2 == 0:
                                uv_points = coords.reshape(-1, 2)
                            else:
                                print(f"[ANNO_ERROR] Invalid uv format in lane annotation: {len(coords)} values")
                                uv_points = np.zeros((lane_points.shape[0], 2), dtype=np.float32)
                        
                        # 确保UV点数与XYZ点数匹配
                        if len(uv_points) != len(lane_points):
                            print(f"[ANNO_ERROR] UV points count ({len(uv_points)}) does not match XYZ points count ({len(lane_points)})")
                            # 如果不匹配，使用零填充创建相同大小的UV点数组
                            uv_points = np.zeros((lane_points.shape[0], 2), dtype=np.float32)
                        
                        gt_lane_points_uvs.append(uv_points)
                    else:
                        # 如果没有UV坐标，添加零数组
                        gt_lane_points_uvs.append(np.zeros((lane_points.shape[0], 2), dtype=np.float32))
        
        if len(gt_lanes_3d) > 0:
            print(f"[LANE_ANNO_INFO] Found {len(gt_lanes_3d)} lanes, first lane has {len(gt_lanes_3d[0])} points")
            
            # Print lane type distribution stats
            print("[LOADING_LANES] Loaded lane labels:", gt_lane_labels)
            
            # Print lane type distribution with class names if available
            if hasattr(self, 'lane_classes') and self.lane_classes is not None:
                print("[LOADING_LANES] Lane type distribution:")
                for lane_type, count in lane_type_counts.items():
                    if lane_type < len(self.lane_classes):
                        lane_class_name = self.lane_classes[lane_type]
                    else:
                        lane_class_name = f"Unknown (id: {lane_type})"
                    print(f"  - {lane_class_name} (ID: {lane_type}): {count} lanes")
            else:
                print("[LOADING_LANES] Lane type distribution:", 
                      {f"ID {k}": v for k, v in lane_type_counts.items()})
        else:
            print("[LANE_ANNO_WARN] No lane annotations found for this frame")
            
        anns_results = dict(
            gt_lanes_3d=gt_lanes_3d,
            gt_lane_labels=gt_lane_labels,
            gt_lane_points_uvs=gt_lane_points_uvs
        )
        return anns_results

    def prepare_train_data(self, index):
        """Training data preparation for lane detection.

        Args:
            index (int): Index for accessing the target data.

        Returns:
            dict: Training data dict of the corresponding index.
        """
        input_dict = self.get_data_info(index)
        if input_dict is None:
            return None
        
        # 确保lidar_path存在，这是loading.py中LoadPointsFromFile类所需的
        if 'lidar_path' not in input_dict or input_dict['lidar_path'] is None:
            print(f"[DATA_ERROR] Missing lidar_path for index {index}, skipping sample")
            return None
            
        # 确保image_paths存在，这是loading.py中LoadMultiViewImageFromFiles类所需的
        if 'image_paths' not in input_dict or not input_dict['image_paths']:
            print(f"[DATA_WARNING] Missing image_paths for index {index}, setting empty list")
            input_dict['image_paths'] = []
        
        # 预处理数据
        self.pre_pipeline(input_dict)
        
        try:
            # 运行pipeline处理数据
            example = self.pipeline(input_dict)
        except Exception as e:
            print(f"[PIPELINE_ERROR] Error processing sample {index}: {e}")
            import traceback
            traceback.print_exc()
            return None
        
        # 判断车道线是否为空
        if self.filter_empty_gt and example is not None:
            if "gt_lanes_3d" not in example:
                return None
            if example["gt_lanes_3d"] is None:
                return None
            if len(example["gt_lanes_3d"]) == 0:
                return None
                
        return example

    def evaluate(self, results, logger=None, **kwargs):
        """Evaluate the dataset.

        Args:
            results (list): Testing results of the dataset.
            logger (logging.Logger | str | None): Logger used for printing
                related information during evaluation. Default: None.
            **kwargs: Other arguments.

        Returns:
            dict: Evaluation results.
        """
        if self.eval_version == 'OpenLane':
            return self._evaluate_openlane(results, logger, **kwargs)
        else:
            raise NotImplementedError(f"Evaluation version {self.eval_version} not supported")

    def _evaluate_openlane(self, results, logger=None, **kwargs):
        """OpenLane-style evaluation for 3D lane detection.

        Args:
            results (list): Testing results of the dataset. 
            logger (logging.Logger | str | None): Logger used for printing
                related information during evaluation. Default: None.
            **kwargs: Other arguments.

        Returns:
            dict: Evaluation results.
        """
        try:
            from mmdet3d.datasets.pipelines.loading_utils import prune_3d_lane_by_range, prune_3d_lane_by_visibility
            from mmdet3d.datasets.pipelines.loading_utils import resample_laneline_in_y
            from mmdet3d.utils.min_cost_flow import SolveMinCostFlow
        except ImportError:
            # Define functions locally if not available in the module
            def prune_3d_lane_by_visibility(lane_3d, visibility):
                """Prune 3D lane by visibility."""
                lane_3d = lane_3d[visibility > 0, ...]
                return lane_3d

            def prune_3d_lane_by_range(lane_3d, x_min, x_max):
                """Prune 3D lane by range."""
                # Remove lane points out of y range
                lane_3d = lane_3d[np.logical_and(lane_3d[:, 1] > 0, lane_3d[:, 1] < 200), ...]
                # Remove lane points out of x range
                lane_3d = lane_3d[np.logical_and(lane_3d[:, 0] > x_min,
                                               lane_3d[:, 0] < x_max), ...]
                return lane_3d

            def resample_laneline_in_y(input_lane, y_steps, out_vis=False):
                """Interpolate x, z values at each anchor grid."""
                # Ensure at least two points in lane
                assert(input_lane.shape[0] >= 2)
                
                y_min = np.min(input_lane[:, 1])-5
                y_max = np.max(input_lane[:, 1])+5
                
                # Add z dimension if not present
                if input_lane.shape[1] < 3:
                    input_lane = np.concatenate([input_lane, np.zeros([input_lane.shape[0], 1], dtype=np.float32)], axis=1)
                
                # Interpolate x and z values at each y step
                from scipy.interpolate import interp1d
                f_x = interp1d(input_lane[:, 1], input_lane[:, 0], fill_value="extrapolate")
                f_z = interp1d(input_lane[:, 1], input_lane[:, 2], fill_value="extrapolate")
                
                x_values = f_x(y_steps)
                z_values = f_z(y_steps)
                
                if out_vis:
                    output_visibility = np.logical_and(y_steps >= y_min, y_steps <= y_max)
                    return x_values, z_values, output_visibility.astype(np.float32) + 1e-9
                return x_values, z_values
            
            # Import SolveMinCostFlow implementation or define it
            def SolveMinCostFlow(adj_mat, cost_mat):
                """
                Solve minimum cost flow problem for lane matching.
                This is a simplified implementation when ortools is not available.
                
                Args:
                    adj_mat: Adjacency matrix (binary) indicating possible matches
                    cost_mat: Cost matrix indicating matching costs
                    
                Returns:
                    List of [gt_idx, pred_idx, cost] matches
                """
                try:
                    from ortools.graph import pywrapgraph
                    
                    # Implementation similar to OpenLane's MinCostFlow.py
                    min_cost_flow = pywrapgraph.SimpleMinCostFlow()
                    
                    cnt_1, cnt_2 = adj_mat.shape
                    cnt_nonzero_row = int(np.sum(np.sum(adj_mat, axis=1) > 0))
                    cnt_nonzero_col = int(np.sum(np.sum(adj_mat, axis=0) > 0))
                    
                    # Prepare directed graph for the flow
                    start_nodes = np.zeros(cnt_1, dtype=np.int).tolist() +\
                                np.repeat(np.array(range(1, cnt_1+1)), cnt_2).tolist() + \
                                [i for i in range(cnt_1+1, cnt_1 + cnt_2 + 1)]
                    end_nodes = [i for i in range(1, cnt_1+1)] + \
                                np.repeat(np.array([i for i in range(cnt_1+1, cnt_1 + cnt_2 + 1)]).reshape([1, -1]), cnt_1, axis=0).flatten().tolist() + \
                                [cnt_1 + cnt_2 + 1 for i in range(cnt_2)]
                    capacities = np.ones(cnt_1, dtype=np.int).tolist() + adj_mat.flatten().astype(np.int).tolist() + np.ones(cnt_2, dtype=np.int).tolist()
                    costs = (np.zeros(cnt_1, dtype=np.int).tolist() + cost_mat.flatten().astype(np.int).tolist() + np.zeros(cnt_2, dtype=np.int).tolist())
                    # Define supplies at each node
                    supplies = [min(cnt_nonzero_row, cnt_nonzero_col)] + np.zeros(cnt_1 + cnt_2, dtype=np.int).tolist() + [-min(cnt_nonzero_row, cnt_nonzero_col)]
                    source = 0
                    sink = cnt_1 + cnt_2 + 1
                    
                    # Add each arc
                    for i in range(len(start_nodes)):
                        min_cost_flow.AddArcWithCapacityAndUnitCost(start_nodes[i], end_nodes[i],
                                                                capacities[i], costs[i])
                    
                    # Add node supplies
                    for i in range(len(supplies)):
                        min_cost_flow.SetNodeSupply(i, supplies[i])
                    
                    match_results = []
                    # Find the minimum cost flow
                    if min_cost_flow.Solve() == min_cost_flow.OPTIMAL:
                        for arc in range(min_cost_flow.NumArcs()):
                            # Ignore arcs from source or to sink
                            if min_cost_flow.Tail(arc)!=source and min_cost_flow.Head(arc)!=sink:
                                # Arcs with flow value > 0 represent matches
                                if min_cost_flow.Flow(arc) > 0:
                                    match_results.append([min_cost_flow.Tail(arc)-1,
                                                        min_cost_flow.Head(arc)-cnt_1-1,
                                                        min_cost_flow.UnitCost(arc)])
                    
                    return match_results
                except ImportError:
                    # Fallback to greedy matching if ortools is not available
                    matches = []
                    # Create a copy of cost matrix to modify
                    costs = cost_mat.copy()
                    # Set unconnected pairs to infinite cost
                    costs[adj_mat == 0] = float('inf')
                    
                    # While there are valid matches to make
                    while np.min(costs) < float('inf'):
                        # Find minimum cost match
                        i, j = np.unravel_index(np.argmin(costs), costs.shape)
                        # Add to matches
                        matches.append([i, j, costs[i, j]])
                        # Remove these rows/cols from consideration
                        costs[i, :] = float('inf')
                        costs[:, j] = float('inf')
                    
                    return matches

        # Extract evaluation parameters from kwargs
        eval_params = kwargs.get('eval_params', {})
        metric_list = eval_params.get('metric_list', [
            'f1_score', 'precision', 'recall', 'x_error_near', 'x_error_far', 'z_error'
        ])
        iou_threshold = eval_params.get('iou_threshold', 0.5)

        # Define evaluation constants
        y_samples = np.linspace(3, 103, num=100, endpoint=False)  # Sample points along y-axis
        close_range = 40  # Close range threshold (in meters)
        close_range_idx = np.where(y_samples > close_range)[0][0]  # Index for close range
        dist_th = 1.5  # Distance threshold for lane point matching (in meters)
        ratio_th = 0.75  # Ratio threshold for lane matching

        # Get the point cloud range from dataset
        point_cloud_range = getattr(self, 'point_cloud_range', [0.0, -50.0, -1.0, 80.0, 50.0, 3.0])
        x_min, y_min, _, x_max, y_max, _ = point_cloud_range

        # Initialize statistics containers
        laneline_stats = []
        laneline_x_error_close = []
        laneline_x_error_far = []
        laneline_z_error_close = []
        laneline_z_error_far = []

        for idx, result in enumerate(results):
            if idx % 100 == 0 and logger is not None:
                logger.info(f'Evaluating {idx}/{len(results)}')
            
            # Extract prediction and ground truth
            if 'pred_lanes' not in result or 'gt_lanes_3d' not in result:
                continue
                
            pred_lanes = result.get('pred_lanes', [])
            pred_lane_types = result.get('pred_lane_types', [])
            
            gt_lanes = result.get('gt_lanes_3d', [])
            gt_visibility = result.get('gt_lane_visibility', [])
            gt_lane_types = result.get('gt_lane_labels', [])
            
            # Ensure gt_visibility exists for each gt lane
            if not gt_visibility:
                gt_visibility = [np.ones(len(lane)) for lane in gt_lanes]
            
            # Skip if no predictions or ground truth
            if len(pred_lanes) == 0 or len(gt_lanes) == 0:
                continue
            
            # Convert to numpy arrays if needed
            pred_lanes = [np.array(lane) if not isinstance(lane, np.ndarray) else lane for lane in pred_lanes]
            gt_lanes = [np.array(lane) if not isinstance(lane, np.ndarray) else lane for lane in gt_lanes]
            
            # Ensure lane_types have the same length as lanes
            if len(pred_lane_types) < len(pred_lanes):
                pred_lane_types = list(pred_lane_types) + [0] * (len(pred_lanes) - len(pred_lane_types))
            if len(gt_lane_types) < len(gt_lanes):
                gt_lane_types = list(gt_lane_types) + [0] * (len(gt_lanes) - len(gt_lane_types))
            
            # Prune lanes by visibility
            gt_lanes = [prune_3d_lane_by_visibility(lane, vis) if vis is not None else lane 
                        for lane, vis in zip(gt_lanes, gt_visibility)]
            
            # Filter lanes based on sampling range (y-axis)
            pred_lanes_in_range = []
            pred_types_in_range = []
            for lane, lane_type in zip(pred_lanes, pred_lane_types):
                if lane.shape[0] <= 1:
                    continue
                if lane[0, 1] < y_samples[-1] and lane[-1, 1] > y_samples[0]:
                    lane = prune_3d_lane_by_range(lane, x_min, x_max)
                    if lane.shape[0] > 1:
                        pred_lanes_in_range.append(lane)
                        pred_types_in_range.append(lane_type)
            
            gt_lanes_in_range = []
            gt_types_in_range = []
            gt_vis_in_range = []
            for lane, lane_type, vis in zip(gt_lanes, gt_lane_types, gt_visibility):
                if lane.shape[0] <= 1:
                    continue
                if lane[0, 1] < y_samples[-1] and lane[-1, 1] > y_samples[0]:
                    lane = prune_3d_lane_by_range(lane, x_min, x_max)
                    if lane.shape[0] > 1:
                        gt_lanes_in_range.append(lane)
                        gt_types_in_range.append(lane_type)
                        gt_vis_in_range.append(vis)
            
            # Count lanes
            cnt_gt = len(gt_lanes_in_range)
            cnt_pred = len(pred_lanes_in_range)
            
            # If no valid lanes, continue
            if cnt_gt == 0 or cnt_pred == 0:
                continue
            
            # Initialize visibility matrices
            gt_visibility_mat = np.zeros((cnt_gt, 100))
            pred_visibility_mat = np.zeros((cnt_pred, 100))
            
            # Resample lanes at fixed y positions for matching
            for i in range(cnt_gt):
                gt_lane = gt_lanes_in_range[i]
                min_y = np.min(gt_lane[:, 1])
                max_y = np.max(gt_lane[:, 1])
                x_values, z_values, visibility_vec = resample_laneline_in_y(gt_lane, y_samples, out_vis=True)
                gt_lanes_in_range[i] = np.vstack([x_values, z_values]).T
                gt_visibility_mat[i, :] = np.logical_and(
                    x_values >= x_min, 
                    np.logical_and(
                        x_values <= x_max,
                        np.logical_and(
                            y_samples >= min_y, 
                            y_samples <= max_y
                        )
                    )
                )
                gt_visibility_mat[i, :] = np.logical_and(gt_visibility_mat[i, :], visibility_vec)
            
            for i in range(cnt_pred):
                pred_lane = pred_lanes_in_range[i]
                min_y = np.min(pred_lane[:, 1])
                max_y = np.max(pred_lane[:, 1])
                x_values, z_values, visibility_vec = resample_laneline_in_y(pred_lane, y_samples, out_vis=True)
                pred_lanes_in_range[i] = np.vstack([x_values, z_values]).T
                pred_visibility_mat[i, :] = np.logical_and(
                    x_values >= x_min, 
                    np.logical_and(
                        x_values <= x_max,
                        np.logical_and(
                            y_samples >= min_y, 
                            y_samples <= max_y
                        )
                    )
                )
                pred_visibility_mat[i, :] = np.logical_and(pred_visibility_mat[i, :], visibility_vec)
            
            # Filter lanes with at least two valid points
            valid_gt_indices = np.where(np.sum(gt_visibility_mat, axis=1) > 1)[0]
            gt_lanes_in_range = [gt_lanes_in_range[i] for i in valid_gt_indices]
            gt_types_in_range = [gt_types_in_range[i] for i in valid_gt_indices]
            gt_visibility_mat = gt_visibility_mat[valid_gt_indices]
            
            valid_pred_indices = np.where(np.sum(pred_visibility_mat, axis=1) > 1)[0]
            pred_lanes_in_range = [pred_lanes_in_range[i] for i in valid_pred_indices]
            pred_types_in_range = [pred_types_in_range[i] for i in valid_pred_indices]
            pred_visibility_mat = pred_visibility_mat[valid_pred_indices]
            
            # Update counts after filtering
            cnt_gt = len(gt_lanes_in_range)
            cnt_pred = len(pred_lanes_in_range)
            
            # If no valid lanes after filtering, continue
            if cnt_gt == 0 or cnt_pred == 0:
                continue
            
            # Initialize matrices for matching
            adj_mat = np.zeros((cnt_gt, cnt_pred), dtype=int)
            cost_mat = np.zeros((cnt_gt, cnt_pred), dtype=int)
            cost_mat.fill(1000)
            num_match_mat = np.zeros((cnt_gt, cnt_pred), dtype=float)
            x_dist_mat_close = np.zeros((cnt_gt, cnt_pred), dtype=float)
            x_dist_mat_close.fill(1000.)
            x_dist_mat_far = np.zeros((cnt_gt, cnt_pred), dtype=float)
            x_dist_mat_far.fill(1000.)
            z_dist_mat_close = np.zeros((cnt_gt, cnt_pred), dtype=float)
            z_dist_mat_close.fill(1000.)
            z_dist_mat_far = np.zeros((cnt_gt, cnt_pred), dtype=float)
            z_dist_mat_far.fill(1000.)
            
            # Compute curve to curve distance for matching
            for i in range(cnt_gt):
                for j in range(cnt_pred):
                    # Compute x and z distances
                    x_dist = np.abs(gt_lanes_in_range[i][:, 0] - pred_lanes_in_range[j][:, 0])
                    z_dist = np.abs(gt_lanes_in_range[i][:, 1] - pred_lanes_in_range[j][:, 1])
                    
                    # Apply visibility to handle partial matching
                    both_visible_indices = np.logical_and(gt_visibility_mat[i, :] >= 0.5, pred_visibility_mat[j, :] >= 0.5)
                    both_invisible_indices = np.logical_and(gt_visibility_mat[i, :] < 0.5, pred_visibility_mat[j, :] < 0.5)
                    other_indices = np.logical_not(np.logical_or(both_visible_indices, both_invisible_indices))
                    
                    # Calculate Euclidean distance
                    euclidean_dist = np.sqrt(x_dist ** 2 + z_dist ** 2)
                    euclidean_dist[both_invisible_indices] = 0
                    euclidean_dist[other_indices] = dist_th
                    
                    # Count points below threshold as matches
                    num_match_mat[i, j] = np.sum(euclidean_dist < dist_th) - np.sum(both_invisible_indices)
                    adj_mat[i, j] = 1
                    
                    # Compute cost for min-cost flow
                    cost_ = np.sum(euclidean_dist)
                    if 0 < cost_ < 1:
                        cost_ = 1
                    else:
                        cost_ = int(cost_)
                    cost_mat[i, j] = cost_
                    
                    # Compute error metrics for close range
                    if np.sum(both_visible_indices[:close_range_idx]) > 0:
                        x_dist_mat_close[i, j] = np.sum(
                            x_dist[:close_range_idx] * both_visible_indices[:close_range_idx]) / np.sum(
                            both_visible_indices[:close_range_idx])
                        z_dist_mat_close[i, j] = np.sum(
                            z_dist[:close_range_idx] * both_visible_indices[:close_range_idx]) / np.sum(
                            both_visible_indices[:close_range_idx])
                    else:
                        x_dist_mat_close[i, j] = -1
                        z_dist_mat_close[i, j] = -1
                    
                    # Compute error metrics for far range
                    if np.sum(both_visible_indices[close_range_idx:]) > 0:
                        x_dist_mat_far[i, j] = np.sum(
                            x_dist[close_range_idx:] * both_visible_indices[close_range_idx:]) / np.sum(
                            both_visible_indices[close_range_idx:])
                        z_dist_mat_far[i, j] = np.sum(
                            z_dist[close_range_idx:] * both_visible_indices[close_range_idx:]) / np.sum(
                            both_visible_indices[close_range_idx:])
                    else:
                        x_dist_mat_far[i, j] = -1
                        z_dist_mat_far[i, j] = -1
            
            # Solve bipartite matching via min cost flow
            match_results = SolveMinCostFlow(adj_mat, cost_mat)
            match_results = np.array(match_results) if len(match_results) > 0 else np.empty((0, 3))
            
            # Count matches and compute metrics
            r_lane, p_lane, c_lane = 0., 0., 0.
            match_gt_ids = []
            match_pred_ids = []
            match_num = 0
            
            if match_results.shape[0] > 0:
                for i in range(len(match_results)):
                    if match_results[i, 2] < dist_th * y_samples.shape[0]:
                        match_num += 1
                        gt_i = match_results[i, 0]
                        pred_i = match_results[i, 1]
                        
                        # Consider match based on ratio of matched points
                        if num_match_mat[gt_i, pred_i] / np.sum(gt_visibility_mat[gt_i, :]) >= ratio_th:
                            r_lane += 1
                            match_gt_ids.append(gt_i)
                        if num_match_mat[gt_i, pred_i] / np.sum(pred_visibility_mat[pred_i, :]) >= ratio_th:
                            p_lane += 1
                            match_pred_ids.append(pred_i)
                        if pred_types_in_range and gt_types_in_range:
                            if pred_types_in_range[pred_i] == gt_types_in_range[gt_i]:
                                c_lane += 1  # Category matched
                        
                        # Collect error metrics
                        if x_dist_mat_close[gt_i, pred_i] > -0.5:
                            laneline_x_error_close.append(x_dist_mat_close[gt_i, pred_i])
                        if x_dist_mat_far[gt_i, pred_i] > -0.5:
                            laneline_x_error_far.append(x_dist_mat_far[gt_i, pred_i])
                        if z_dist_mat_close[gt_i, pred_i] > -0.5:
                            laneline_z_error_close.append(z_dist_mat_close[gt_i, pred_i])
                        if z_dist_mat_far[gt_i, pred_i] > -0.5:
                            laneline_z_error_far.append(z_dist_mat_far[gt_i, pred_i])
            
            # Append statistics for this sample
            laneline_stats.append(np.array([r_lane, p_lane, c_lane, cnt_gt, cnt_pred, match_num]))
        
        # Aggregate statistics
        laneline_stats = np.array(laneline_stats)
        total_gt = np.sum(laneline_stats[:, 3]) if laneline_stats.size > 0 else 0
        total_pred = np.sum(laneline_stats[:, 4]) if laneline_stats.size > 0 else 0
        total_match_gt = np.sum(laneline_stats[:, 0]) if laneline_stats.size > 0 else 0
        total_match_pred = np.sum(laneline_stats[:, 1]) if laneline_stats.size > 0 else 0
        total_match_cat = np.sum(laneline_stats[:, 2]) if laneline_stats.size > 0 else 0
        
        # Calculate metrics
        R_lane = total_match_gt / max(total_gt, 1e-6)  # Recall
        P_lane = total_match_pred / max(total_pred, 1e-6)  # Precision
        F_lane = 2 * R_lane * P_lane / max(R_lane + P_lane, 1e-6)  # F1 score
        C_lane = total_match_cat / max(np.sum(laneline_stats[:, 5]), 1e-6)  # Category accuracy
        
        # Calculate error metrics
        x_error_close_avg = np.mean(laneline_x_error_close) if laneline_x_error_close else 0
        x_error_far_avg = np.mean(laneline_x_error_far) if laneline_x_error_far else 0
        z_error_close_avg = np.mean(laneline_z_error_close) if laneline_z_error_close else 0
        z_error_far_avg = np.mean(laneline_z_error_far) if laneline_z_error_far else 0
        
        # Create results dictionary
        eval_results = {
            'f1_score': float(F_lane),
            'precision': float(P_lane),
            'recall': float(R_lane),
            'category_accuracy': float(C_lane),
            'x_error_near': float(x_error_close_avg),
            'x_error_far': float(x_error_far_avg),
            'z_error_near': float(z_error_close_avg),
            'z_error_far': float(z_error_far_avg),
            'num_gt': int(total_gt),
            'num_pred': int(total_pred),
            'num_match_gt': int(total_match_gt),
            'num_match_pred': int(total_match_pred),
            'num_match_category': int(total_match_cat)
        }
        
        # Print results if logger is provided
        if logger is not None:
            logger.info('Lane Detection Evaluation Results:')
            logger.info(f'F1 Score: {F_lane:.4f}')
            logger.info(f'Precision: {P_lane:.4f}')
            logger.info(f'Recall: {R_lane:.4f}')
            logger.info(f'Category Accuracy: {C_lane:.4f}')
            logger.info(f'X Error Near (<{close_range}m): {x_error_close_avg:.4f}m')
            logger.info(f'X Error Far (>{close_range}m): {x_error_far_avg:.4f}m')
            logger.info(f'Z Error Near (<{close_range}m): {z_error_close_avg:.4f}m')
            logger.info(f'Z Error Far (>{close_range}m): {z_error_far_avg:.4f}m')
            logger.info(f'Total GT Lanes: {total_gt}')
            logger.info(f'Total Pred Lanes: {total_pred}')
            logger.info(f'Total Matched GT: {total_match_gt}')
            logger.info(f'Total Matched Pred: {total_match_pred}')
            logger.info(f'Total Category Matches: {total_match_cat}')
        
        return eval_results

    def _update_vehicle_calib(self, calib_data, cam_list):
        """Convert calibration data format to match MogoB2Dataset processing.
        
        Args:
            calib_data (dict): Raw calibration data loaded from JSON file.
            cam_list (list): List of camera names to process.
            
        Returns:
            dict: Processed calibration data with camera-specific entries.
        """
        processed_calib = {}
        
        for cam_name in cam_list:
            if cam_name in calib_data:
                cam_calib = calib_data[cam_name]
                
                # Extract intrinsic matrix (3x3)
                intrinsic = cam_calib.get('intrinsic', [])
                if len(intrinsic) == 9:
                    intrinsic_matrix = np.array(intrinsic).reshape(3, 3)
                else:
                    print(f"[CALIB_WARNING] Invalid intrinsic matrix for camera {cam_name}")
                    intrinsic_matrix = np.eye(3)
                
                # Extract extrinsic matrix (3x4)
                extrinsic = cam_calib.get('extrinsic', [])
                if len(extrinsic) == 12:
                    extrinsic_matrix = np.array(extrinsic).reshape(3, 4)
                else:
                    print(f"[CALIB_WARNING] Invalid extrinsic matrix for camera {cam_name}")
                    extrinsic_matrix = np.hstack([np.eye(3), np.zeros((3, 1))])
                
                processed_calib[cam_name] = {
                    'intrinsic': intrinsic_matrix,
                    'extrinsic': extrinsic_matrix
                }
            else:
                print(f"[CALIB_WARNING] Camera {cam_name} not found in calibration data")
        
        return processed_calib

    def format_results(self, results, jsonfile_prefix=None, **kwargs):
        """Format the results to json (standard format for lane evaluation).
        
        Args:
            results (list[dict]): Testing results of the dataset.
            jsonfile_prefix (str): The prefix of json files. It includes
                the file path and the prefix of filename, e.g., "a/b/prefix".
                If not specified, a temp file will be created.
            **kwargs: Other parameters for format_results.
                
        Returns:
            tuple: (result_files, tmp_dir), result_files is a list containing
                the created json file path(s) of the 3D lane results.
                tmp_dir is the temporal directory created for saving json files
                when jsonfile_prefix is not specified.
        """
        assert isinstance(results, list), 'results must be a list'
        assert len(results) == len(self), \
            f'The length of results is not equal to the dataset length: {len(results)} != {len(self)}'

        if jsonfile_prefix is None:
            tmp_dir = tempfile.TemporaryDirectory()
            jsonfile_prefix = osp.join(tmp_dir.name, 'results')
        else:
            tmp_dir = None
            os.makedirs(osp.dirname(jsonfile_prefix), exist_ok=True)
        
        result_files = self._format_lane_results(results, jsonfile_prefix)
        
        return result_files, tmp_dir

    def _format_lane_results(self, results, jsonfile_prefix):
        """Format the lane detection results as JSON.
        
        Args:
            results (list[dict]): Testing results of the dataset.
            jsonfile_prefix (str): The prefix of json files.
                
        Returns:
            list[str]: Created json file path(s) of the 3D lane results.
        """
        result_files = []
        
        # 创建输出目录
        output_dir = osp.dirname(jsonfile_prefix)
        os.makedirs(output_dir, exist_ok=True)
        
        # 处理每个样本的结果
        for idx, result in enumerate(results):
            # 从data_infos获取帧信息
            info = self.data_infos[idx]
            frame_id = info.get('frame_id', str(idx).zfill(10))
            timestamp = info.get('timestamp', 0)
            segment_id = info.get('segment_id', '')
            
            # 提取车道线预测结果
            lanes = result.get('pred_lanes', [])
            lane_types = result.get('pred_lane_types', [0] * len(lanes))
            lane_scores = result.get('pred_scores', [1.0] * len(lanes))
            
            # 创建输出结构
            output = {
                'frame_id': frame_id,
                'timestamp': timestamp,
                'segment_id': segment_id,
                'lanes': []
            }
            
            # 添加每条车道线到输出
            for lane, lane_type, score in zip(lanes, lane_types, lane_scores):
                # 获取车道线类型名称（如果可能）
                lane_type_name = self.LANE_CLASSES[lane_type] if self.LANE_CLASSES and lane_type < len(self.LANE_CLASSES) else f"class_{lane_type}"
                
                # 转换车道线点为列表（如果是numpy数组或张量）
                if isinstance(lane, np.ndarray):
                    lane = lane.tolist()
                elif isinstance(lane, torch.Tensor):
                    lane = lane.cpu().numpy().tolist()
                
                # 创建车道线条目
                lane_data = {
                    'points_3d': lane,
                    'type_id': int(lane_type),
                    'type_name': lane_type_name,
                    'score': float(score)
                }
                
                output['lanes'].append(lane_data)
            
            # 为此样本创建文件名
            if segment_id:
                # 如果需要，创建segment目录
                segment_dir = osp.join(output_dir, segment_id)
                os.makedirs(segment_dir, exist_ok=True)
                # 保存在segment目录中
                result_file = osp.join(segment_dir, f"{frame_id}.json")
            else:
                # 直接保存在输出目录中
                result_file = osp.join(output_dir, f"{frame_id}.json")
            
            # 写入文件
            with open(result_file, 'w') as f:
                json.dump(output, f, indent=2)
            
            result_files.append(result_file)
        
        return result_files

    def pre_pipeline(self, results):
        """Initialization before data preparation."""
        results["img_fields"] = []
        results["pts_mask_fields"] = []
        results["pts_seg_fields"] = []
        results["bbox_fields"] = []
        results["mask_fields"] = []
        results["seg_fields"] = []
        results["dataset_root"] = self.dataset_root
import numpy as np
import torch
from mmcv.parallel import DataContainer as DC

from mmdet.datasets.builder import PIPELINES


@PIPELINES.register_module()
class GenerateBEVLaneHeatmapTargets:
    """Generate BEV lane targets for heatmap-based lane head.
    
    This transform generates the targets for training the BEVLaneHeatmapHead,
    including:
    - Heatmap of lane points
    - X-offset for precise localization
    - Z-height values
    - Lane classification
    - Valid mask
    - Instance IDs for instance-level grouping (new!)
    """
    
    def __init__(self,
                 point_cloud_range,
                 voxel_size,
                 grid_size=None,
                 lane_classes=None,
                 target_config=None):
        """Initialize GenerateBEVLaneHeatmapTargets.
        
        Args:
            point_cloud_range (list): [x_min, y_min, z_min, x_max, y_max, z_max]
            voxel_size (list): [x_res, y_res, z_res]
            grid_size (list, optional): BEV grid size [x_size, y_size]
            lane_classes (list, optional): List of lane class names.
            target_config (dict, optional): Additional config for target generation.
        """
        self.point_cloud_range = point_cloud_range
        self.voxel_size = voxel_size
        
        # Extract BEV grid parameters
        self.x_min, self.y_min, self.z_min = point_cloud_range[:3]
        self.x_max, self.y_max, self.z_max = point_cloud_range[3:]
        self.x_res, self.y_res, self.z_res = voxel_size
        
        # Calculate grid size if not provided
        if grid_size is None:
            self.grid_size = [
                int((self.x_max - self.x_min) / self.x_res),
                int((self.y_max - self.y_min) / self.y_res)
            ]
        else:
            self.grid_size = grid_size
            
        self.nx, self.ny = self.grid_size
        print(f"[TARGET_GEN_INIT] Grid size: nx={self.nx}, ny={self.ny}, point_cloud_range={point_cloud_range}, voxel_size={voxel_size}")
        
        # === CRITICAL DEBUG: Verify grid dimensions are FPN-compatible ===
        print(f"[TARGET_GEN_INIT] === GRID DIMENSION COMPATIBILITY CHECK ===")
        print(f"[TARGET_GEN_INIT] BEV grid size: [{self.nx}, {self.ny}]")
        
        # Check if dimensions are multiples of 8 (required for better downsampling alignment)
        if self.nx % 8 != 0:
            print(f"[TARGET_GEN_WARN] Grid X dimension ({self.nx}) is not a multiple of 8!")
            print(f"[TARGET_GEN_WARN] This may cause alignment issues with deeper feature downsampling.")
        else:
            print(f"[TARGET_GEN_INFO] Grid X dimension ({self.nx}) is excellent for deep feature alignment (multiple of 8).")
            
        if self.ny % 8 != 0:
            print(f"[TARGET_GEN_WARN] Grid Y dimension ({self.ny}) is not a multiple of 8!")
            print(f"[TARGET_GEN_WARN] This may cause alignment issues with deeper feature downsampling.")
        else:
            print(f"[TARGET_GEN_INFO] Grid Y dimension ({self.ny}) is excellent for deep feature alignment (multiple of 8).")
        
        # Calculate expected feature map sizes after SECOND backbone downsampling
        # SECOND backbone has strides [1, 1, 2, 2], so final features will be downsampled by 4x
        expected_x_after_downsample = self.nx // 4
        expected_y_after_downsample = self.ny // 4
        print(f"[TARGET_GEN_INFO] Expected feature sizes after SECOND downsampling:")
        print(f"[TARGET_GEN_INFO]   Level 0&1: [{self.nx}, {self.ny}] (stride 1)")
        print(f"[TARGET_GEN_INFO]   Level 2: [{self.nx//2}, {self.ny//2}] (stride 2)")  
        print(f"[TARGET_GEN_INFO]   Level 3: [{expected_x_after_downsample}, {expected_y_after_downsample}] (stride 4)")
        
        # Verify high-resolution grid suitability
        grid_resolution_m = min(self.x_res, self.y_res)
        print(f"[TARGET_GEN_INFO] Grid resolution: {grid_resolution_m}m per cell")
        if grid_resolution_m <= 0.3:
            print(f"[TARGET_GEN_INFO] High-resolution grid ({grid_resolution_m}m) excellent for lane detection!")
        else:
            print(f"[TARGET_GEN_WARN] Grid resolution ({grid_resolution_m}m) may be too coarse for precise lane detection.")
        
        print(f"[TARGET_GEN_INIT] =======================================")
        
        # Lane classes
        self.lane_classes = lane_classes
        self.num_classes = len(lane_classes) if lane_classes is not None else 1
        
        # Default target config
        self.target_config = {
            'gaussian_sigma': 1.0,
            'heatmap_radius': 2,
            'max_lanes': 20,
            'num_points': 100,  # Points to sample along y-axis (longitudinal direction)
            'generate_instance_ids': False,  # New: whether to generate instance ID targets
        }
        
        # Update with user config if provided
        if target_config is not None:
            self.target_config.update(target_config)

        # CRITICAL FIX: 车道线沿y轴(纵向)延伸，应该按y轴进行插值采样
        # 原来错误地按x轴采样，现在修正为按y轴采样
        print(f"[COORDINATE_FIX] Switching from x-axis to y-axis sampling for lane interpolation")
        print(f"[COORDINATE_FIX] Y-range: {self.y_min} to {self.y_max}, num_points: {self.target_config['num_points']}")
        self.y_positions = np.linspace(self.y_min, self.y_max, self.target_config['num_points'])
    
    def _get_gaussian_kernel(self, center, sigma=1):
        """Generate a 2D Gaussian kernel.
        
        Args:
            center (tuple): (x, y) center of the kernel.
            sigma (float): Standard deviation of the Gaussian.
            
        Returns:
            np.ndarray: 2D Gaussian kernel.
        """
        x = np.arange(0, self.grid_size[0], dtype=np.float32)
        y = np.arange(0, self.grid_size[1], dtype=np.float32)
        
        # Generate meshgrid
        y, x = np.meshgrid(y, x, indexing='ij')
        
        # Compute Gaussian
        g = np.exp(-((x - center[0])**2 + (y - center[1])**2) / (2 * sigma**2))
        return g
    
    def _get_lane_points(self, lane_3d):
        """Extract lane points from 3D lane data.
        
        Args:
            lane_3d (dict): 3D lane data.
            
        Returns:
            tuple: Points (x, y, z) and visibility.
        """
        # Extract 3D lane points and visibility
        if isinstance(lane_3d, dict) and 'points_3d' in lane_3d:
            points_3d = lane_3d['points_3d']
        elif isinstance(lane_3d, torch.Tensor):
            points_3d = lane_3d.cpu().numpy()
        else:
            points_3d = np.array(lane_3d)
        
        # Extract x, y, z coordinates
        if isinstance(points_3d, torch.Tensor):
            lane_x = points_3d[:, 0].cpu().numpy()
            lane_y = points_3d[:, 1].cpu().numpy()
            lane_z = points_3d[:, 2].cpu().numpy()
        else:
            lane_x = points_3d[:, 0]
            lane_y = points_3d[:, 1]
            lane_z = points_3d[:, 2]
        
        # Extract visibility information if available
        if isinstance(lane_3d, dict) and 'visibility' in lane_3d:
            vis = lane_3d['visibility']
            if isinstance(vis, torch.Tensor):
                vis = vis.cpu().numpy()
        else:
            vis = np.ones_like(lane_x)
        
        return lane_x, lane_y, lane_z, vis
    
    def _interpolate_lane(self, lane_x, lane_y, lane_z, vis, y_positions):
        """Interpolate lane points at specified y positions.

        CRITICAL FIX: 车道线沿y轴(纵向)延伸，应该在固定y位置插值x和z坐标

        Args:
            lane_x (np.ndarray): Lane x-coordinates.
            lane_y (np.ndarray): Lane y-coordinates.
            lane_z (np.ndarray): Lane z-coordinates.
            vis (np.ndarray): Lane visibility.
            y_positions (np.ndarray): Y positions for interpolation.

        Returns:
            tuple: Interpolated x, z coordinates and visibility.
        """
        # Use more robust interpolation that can handle sparse points
        if len(lane_y) < 2:
            print(f"[INTERPOLATE_WARN] Only {len(lane_y)} points available - need at least 2")
            return None, None, None

        # CRITICAL FIX: Sort points by y-coordinate (纵向) instead of x-coordinate
        sort_idx = np.argsort(lane_y)
        lane_x = lane_x[sort_idx]
        lane_y = lane_y[sort_idx]
        lane_z = lane_z[sort_idx]
        vis = vis[sort_idx]

        # Check for sufficient y-range coverage
        y_min, y_max = lane_y.min(), lane_y.max()
        y_range = y_max - y_min

        # More detailed debugging for lane y-range
        # print(f"[INTERPOLATE_DEBUG] Lane y-range details:")
        # print(f"[INTERPOLATE_DEBUG]   - y_min: {y_min:.4f}, y_max: {y_max:.4f}, range: {y_range:.4f}m")
        # print(f"[INTERPOLATE_DEBUG]   - y_positions range: {y_positions.min():.4f} to {y_positions.max():.4f}")
        # print(f"[INTERPOLATE_DEBUG]   - lane has {len(lane_y)} original points")

        if y_range < 1.0:  # Too short - less than 1m
            # print(f"[INTERPOLATE_WARN] Lane too short: {y_range:.2f}m ({y_min:.2f} to {y_max:.2f})")
            # Continue processing anyway for short lanes instead of returning None
            # return None, None, None
            pass # Keep processing short lanes

        # More robust check for intersection with sampling range
        valid_idx = (y_positions >= y_min) & (y_positions <= y_max)
        valid_count = np.sum(valid_idx)

        if valid_count == 0:
            # print(f"[INTERPOLATE_WARN] No intersection with sampling range ({y_min:.2f}-{y_max:.2f})")
            return None, None, None

        # print(f"[INTERPOLATE_INFO] Lane y-range: {y_min:.2f} to {y_max:.2f}, {valid_count}/{len(y_positions)} valid points")
        # Continue with interpolation

        # CRITICAL FIX: Interpolate x and z at specified y positions
        # Create full arrays with NaN for invalid positions
        x_full = np.full_like(y_positions, np.nan)
        z_full = np.full_like(y_positions, np.nan)
        vis_full = np.zeros_like(y_positions)

        # Only interpolate for valid y positions (within lane range)
        y_valid = y_positions[valid_idx]
        x_interp = np.interp(y_valid, lane_y, lane_x)
        z_interp = np.interp(y_valid, lane_y, lane_z)
        vis_interp = np.interp(y_valid, lane_y, vis)
        vis_interp = (vis_interp > 0.5).astype(np.float32)

        # Fill in valid positions
        x_full[valid_idx] = x_interp
        z_full[valid_idx] = z_interp
        vis_full[valid_idx] = vis_interp

        return x_full, z_full, vis_full
    
    def _convert_to_grid(self, x, y):
        """Convert world coordinates to grid indices.
        
        Args:
            x (np.ndarray): X-coordinates in world space.
            y (np.ndarray): Y-coordinates in world space.
            
        Returns:
            tuple: Grid indices (x_idx, y_idx).
        """
        x_idx = (x - self.x_min) / self.x_res
        y_idx = (y - self.y_min) / self.y_res
        
        # Debug coordinate checks more thoroughly
        # if np.any(~np.isnan(x)):
        #     print(f"[GRID_DEBUG] X world coords range: {np.nanmin(x):.2f} to {np.nanmax(x):.2f}, "
        #           f"grid indices: {np.nanmin(x_idx):.2f} to {np.nanmax(x_idx):.2f}, "
        #           f"grid limits: [0, {self.nx-1}]")
        # 
        # if np.any(~np.isnan(y)):
        #     print(f"[GRID_DEBUG] Y world coords range: {np.nanmin(y):.2f} to {np.nanmax(y):.2f}, "
        #           f"grid indices: {np.nanmin(y_idx):.2f} to {np.nanmax(y_idx):.2f}, "
        #           f"grid limits: [0, {self.ny-1}]")
        
        return x_idx, y_idx
    
    def _adjust_lane_labels(self, lane_labels):
        """Adjust lane labels to properly match model expectations.

        CRITICAL FIX: Convert 1-indexed annotation labels (1-12) to 0-indexed model labels (0-11).
        The original annotations use 1-indexed lane type IDs from lane_line_config_v1.yaml,
        but the model expects 0-indexed class indices for proper loss computation.

        Args:
            lane_labels (list): Original lane class labels (1-indexed: 1-12).

        Returns:
            list: Adjusted lane class labels (0-indexed: 0-11).
        """
        if not lane_labels:
            return lane_labels

        # Analyze the distribution of labels for debugging
        unique_labels = set(lane_labels)
        min_label = min(lane_labels) if lane_labels else 0
        max_label = max(lane_labels) if lane_labels else 0

        print(f"[LABEL_ADJUST] === LANE LABEL CONVERSION ===")
        print(f"[LABEL_ADJUST] Original labels: {sorted(list(unique_labels))}")
        print(f"[LABEL_ADJUST] Label range: {min_label} to {max_label}")
        print(f"[LABEL_ADJUST] Total lane classes defined: {len(self.lane_classes) if self.lane_classes else 0}")

        # Check if labels are 1-indexed (typical pattern: labels >= 1)
        all_greater_than_zero = all(label >= 1 for label in lane_labels)
        has_typical_lane_types = any(label in [1, 2, 7, 8] for label in unique_labels)  # Common lane types

        # CRITICAL: Always convert 1-indexed to 0-indexed for proper model training
        if all_greater_than_zero and max_label <= 12 and has_typical_lane_types:
            print(f"[LABEL_ADJUST] Converting 1-indexed labels to 0-indexed for model compatibility")
            adjusted_labels = []

            for label in lane_labels:
                if label >= 1 and label <= 12:
                    # Convert 1-indexed to 0-indexed: 1->0, 2->1, ..., 12->11
                    adjusted_label = label - 1
                    adjusted_labels.append(adjusted_label)

                    # Debug mapping for first few conversions
                    if len(adjusted_labels) <= 3:
                        lane_type_name = self.lane_classes[adjusted_label] if self.lane_classes and adjusted_label < len(self.lane_classes) else f"Class_{adjusted_label}"
                        print(f"[LABEL_ADJUST] Converted: {label} -> {adjusted_label} ({lane_type_name})")
                else:
                    print(f"[LABEL_ADJUST] WARNING: Invalid label {label}, skipping")
                    continue

            print(f"[LABEL_ADJUST] Conversion complete: {len(lane_labels)} -> {len(adjusted_labels)} valid labels")
            print(f"[LABEL_ADJUST] Adjusted labels range: {min(adjusted_labels) if adjusted_labels else 'N/A'} to {max(adjusted_labels) if adjusted_labels else 'N/A'}")
            print(f"[LABEL_ADJUST] ===============================")
            return adjusted_labels
        else:
            print(f"[LABEL_ADJUST] Labels appear to be already 0-indexed or invalid, using as-is")
            print(f"[LABEL_ADJUST] ===============================")
            return lane_labels

    def __call__(self, results):
        """Call function to generate BEV lane targets.
        
        Args:
            results (dict): Result dict containing lane annotations.
            
        Returns:
            dict: Updated result dict with lane targets.
        """
        # Get 3D lane annotations
        gt_lanes_3d = results.get('gt_lanes_3d', [])
        gt_lane_labels = results.get('gt_lane_labels', [])
        
        # Adjust lane labels to handle 1-indexed IDs if needed
        if self.lane_classes and gt_lane_labels:
            gt_lane_labels = self._adjust_lane_labels(gt_lane_labels)
        
        # Enhanced validation for lane class labels (now 0-indexed after adjustment)
        valid_lane_labels = []
        valid_lanes_3d = []

        print(f"[TARGET_GEN_DEBUG] === LANE CLASS VALIDATION ===")
        print(f"[TARGET_GEN_DEBUG] Number of classes: {self.num_classes}")
        print(f"[TARGET_GEN_DEBUG] Lane classes: {self.lane_classes}")
        print(f"[TARGET_GEN_DEBUG] Adjusted labels (0-indexed): {gt_lane_labels}")

        for i, label in enumerate(gt_lane_labels):
            # After _adjust_lane_labels, labels should now be 0-indexed (0-11)
            model_label = label

            # Validate that label is within valid range for 0-indexed classes
            if label < 0 or label >= self.num_classes:
                class_name = f"Invalid (id: {label})"
                print(f"[TARGET_GEN_WARN] Skipping lane with invalid class label: {label} (valid range: 0-{self.num_classes-1})")
                print(f"[TARGET_GEN_WARN]   - This lane had {len(gt_lanes_3d[i])} points")
                continue
            else:
                class_name = self.lane_classes[label] if self.lane_classes else f"Class {label}"
                print(f"[TARGET_GEN_DEBUG] Lane {i}: label={label} ({class_name}), points={len(gt_lanes_3d[i])}")

            valid_lane_labels.append(model_label)
            valid_lanes_3d.append(gt_lanes_3d[i])

        print(f"[TARGET_GEN_DEBUG] Validation complete: {len(valid_lane_labels)}/{len(gt_lane_labels)} lanes valid")
        print(f"[TARGET_GEN_DEBUG] ================================")
        
        # Use validated lanes for processing
        gt_lanes_3d = valid_lanes_3d
        gt_lane_labels = valid_lane_labels
        
        if len(gt_lanes_3d) == 0:
            print("[TARGET_GEN_WARN] No lane annotations found, creating empty targets")
            # No lane annotations, create empty targets with batch dimension
            heatmap = np.zeros((1, 1, self.nx, self.ny), dtype=np.float32)
            offset = np.zeros((1, 1, self.nx, self.ny), dtype=np.float32)
            z_map = np.zeros((1, 1, self.nx, self.ny), dtype=np.float32)
            mask = np.zeros((1, 1, self.nx, self.ny), dtype=np.float32)
            cls_map = np.zeros((1, self.num_classes, self.nx, self.ny), dtype=np.float32)
            instance_ids = np.zeros((1, self.nx, self.ny), dtype=np.int32)
        else:
            print(f"[TARGET_GEN_INFO] Processing {len(gt_lanes_3d)} lanes with labels {gt_lane_labels}")
            # Initialize target maps with batch dimension - using BEV convention [B, C, X, Y]
            heatmap = np.zeros((1, 1, self.nx, self.ny), dtype=np.float32)
            offset = np.zeros((1, 1, self.nx, self.ny), dtype=np.float32)
            z_map = np.zeros((1, 1, self.nx, self.ny), dtype=np.float32)
            mask = np.zeros((1, 1, self.nx, self.ny), dtype=np.float32)
            cls_map = np.zeros((1, self.num_classes, self.nx, self.ny), dtype=np.float32)
            # Initialize instance ID map with batch dimension
            instance_ids = np.zeros((1, self.nx, self.ny), dtype=np.int32)
            
            # Process each lane
            lanes_added = 0
            points_added = 0
            
            for lane_idx, (lane_3d, lane_label) in enumerate(zip(gt_lanes_3d, gt_lane_labels)):
                if lane_idx >= self.target_config['max_lanes']:
                    break
                    
                # Extract lane points
                lane_x, lane_y, lane_z, vis = self._get_lane_points(lane_3d)
                
                # CRITICAL FIX: Interpolate lane at sampled y positions
                x_interp, z_interp, vis_interp = self._interpolate_lane(
                    lane_x, lane_y, lane_z, vis, self.y_positions)

                if x_interp is None:
                    # print(f"[TARGET_GEN_WARN] Lane {lane_idx} could not be interpolated, skipping")
                    continue

                # Convert to grid coordinates
                x_grid, y_grid = self._convert_to_grid(x_interp, self.y_positions)
                
                # Generate targets for valid points
                valid = ~np.isnan(x_interp) & (vis_interp > 0.5)
                valid_count = np.sum(valid)

                if valid_count == 0:
                    # print(f"[TARGET_GEN_WARN] Lane {lane_idx} has no valid points after filtering")
                    continue

                # Instance ID for this lane (1-indexed, 0 is background)
                instance_id = lane_idx + 1 if self.target_config['generate_instance_ids'] else 0

                lane_points_in_grid = 0

                for i in range(len(self.y_positions)):
                    if not valid[i]:
                        continue
                        
                    # Get integer grid cell and offset
                    x_cell = int(np.clip(x_grid[i], 0, self.nx-1)) # Ensure within grid
                    y_cell = int(np.clip(y_grid[i], 0, self.ny-1)) # Ensure within grid
                    
                    # Enhanced debugging for grid mapping
                    # print(f"[GRID_MAPPING] Point {i}: world ({self.x_positions[i]:.2f}, {y_interp[i]:.2f}) -> grid ({x_cell}, {y_cell})")
                    
                    # Skip if outside grid
                    if (x_cell < 0 or x_cell >= self.nx or 
                        y_cell < 0 or y_cell >= self.ny):
                        # print(f"[GRID_MAPPING_WARN] Point outside grid bounds: ({x_cell}, {y_cell})")
                        continue
                    
                    lane_points_in_grid += 1
                    
                    # Add Gaussian to heatmap
                    radius = self.target_config['heatmap_radius']
                    sigma = self.target_config['gaussian_sigma']
                    
                    # Get range for the Gaussian
                    x_min = max(0, x_cell - radius)
                    x_max = min(self.nx, x_cell + radius + 1)
                    y_min = max(0, y_cell - radius)
                    y_max = min(self.ny, y_cell + radius + 1)
                    
                    # Create small Gaussian
                    gaussian = self._get_gaussian_kernel(
                        (x_cell, y_cell), sigma=sigma)
                    
                    # Apply to heatmap (max operation to handle overlapping lanes)
                    # FIXED: Check shape consistency and use proper indexing with batch dimension
                    gaussian_patch = gaussian[y_min:y_max, x_min:x_max]
                    heatmap_patch = heatmap[0, 0, x_min:x_max, y_min:y_max]
                    
                    # Make sure dimensions match
                    if gaussian_patch.shape != heatmap_patch.shape:
                        print(f"[TARGET_GEN_ERROR] Shape mismatch: gaussian_patch={gaussian_patch.shape}, heatmap_patch={heatmap_patch.shape}")
                        print(f"[TARGET_GEN_ERROR] Indices: x_min={x_min}, x_max={x_max}, y_min={y_min}, y_max={y_max}")
                        # Adjust to match shapes using minimum dimensions
                        min_x_shape = min(x_max - x_min, gaussian_patch.shape[1])
                        min_y_shape = min(y_max - y_min, gaussian_patch.shape[0])
                        gaussian_patch = gaussian_patch[:min_y_shape, :min_x_shape]
                        heatmap[0, 0, x_min:x_min+min_x_shape, y_min:y_min+min_y_shape] = np.maximum(
                            heatmap[0, 0, x_min:x_min+min_x_shape, y_min:y_min+min_y_shape],
                            gaussian_patch)
                    else:
                        # Apply as normal - FIXED: Match dimensions properly with batch dimension
                        heatmap[0, 0, x_min:x_max, y_min:y_max] = np.maximum(
                            heatmap[0, 0, x_min:x_max, y_min:y_max],
                            gaussian_patch)
                    
                    # Set other targets with batch dimension
                    x_offset = x_grid[i] - x_cell
                    offset[0, 0, x_cell, y_cell] = x_offset
                    z_map[0, 0, x_cell, y_cell] = z_interp[i]
                    mask[0, 0, x_cell, y_cell] = 1.0

                    # CRITICAL FIX: Ensure lane_label is 0-indexed and within valid range
                    if 0 <= lane_label < self.num_classes:
                        cls_map[0, lane_label, x_cell, y_cell] = 1.0
                        # Debug first few class assignments
                        if lane_points_in_grid <= 3:
                            class_name = self.lane_classes[lane_label] if self.lane_classes else f"Class_{lane_label}"
                            print(f"[CLS_MAP_DEBUG] Setting cls_map[0, {lane_label}, {x_cell}, {y_cell}] = 1.0 ({class_name})")
                    else:
                        print(f"[TARGET_GEN_ERROR] Invalid lane label {lane_label} (valid range: 0-{self.num_classes-1}), skipping cls_map update")
                        print(f"[TARGET_GEN_ERROR] This indicates a problem with label conversion or validation")

                    # Set instance ID if requested
                    if self.target_config['generate_instance_ids']:
                        instance_ids[0, x_cell, y_cell] = instance_id
                
                print(f"[TARGET_GEN_INFO] Lane {lane_idx} (label {lane_label}): {lane_points_in_grid}/{valid_count} points mapped to grid")
                if lane_points_in_grid > 0:
                    lanes_added += 1
                    points_added += lane_points_in_grid
            
            print(f"[TARGET_GEN_SUMMARY] Added {points_added} points from {lanes_added}/{len(gt_lanes_3d)} lanes to targets")

            # CRITICAL: Verify class distribution in generated targets
            print(f"[CLS_DISTRIBUTION_DEBUG] === CLASS DISTRIBUTION IN TARGETS ===")
            for class_idx in range(self.num_classes):
                class_points = np.sum(cls_map[0, class_idx, :, :] > 0.5)
                if class_points > 0:
                    class_name = self.lane_classes[class_idx] if self.lane_classes else f"Class_{class_idx}"
                    print(f"[CLS_DISTRIBUTION_DEBUG] Class {class_idx} ({class_name}): {class_points} points")

            total_cls_points = np.sum(cls_map > 0.5)
            print(f"[CLS_DISTRIBUTION_DEBUG] Total classification points: {total_cls_points}")
            print(f"[CLS_DISTRIBUTION_DEBUG] =======================================")

            # Check if any values were set
            if np.max(heatmap) > 0:
                print(f"[TARGET_GEN_SUCCESS] Heatmap has valid values: min={np.min(heatmap)}, max={np.max(heatmap)}")
            else:
                print(f"[TARGET_GEN_ERROR] All heatmaps are zeros! Check coordinate transformations.")
        
        # 在创建targets字典之前添加调试
        print(f"[TARGET_FORMAT_DEBUG] Target shapes before dictionary creation:")
        print(f"[TARGET_FORMAT_DEBUG] heatmap: {heatmap.shape}")
        print(f"[TARGET_FORMAT_DEBUG] offset: {offset.shape}")
        print(f"[TARGET_FORMAT_DEBUG] z_map: {z_map.shape}")
        print(f"[TARGET_FORMAT_DEBUG] mask: {mask.shape}")
        print(f"[TARGET_FORMAT_DEBUG] cls_map: {cls_map.shape}")
        
        # =============================================================================
        # AUTOGRAD CONNECTIVITY VERIFICATION FOR TARGETS
        # =============================================================================
        print(f"[AUTOGRAD_TARGET_DEBUG] === TARGET AUTOGRAD VERIFICATION ===")
        print(f"[AUTOGRAD_TARGET_DEBUG] Checking if targets will cause autograd issues:")
        
        # Check for potential autograd issues in target creation
        total_positive_heatmap = np.sum(heatmap > 0.1)
        total_positive_mask = np.sum(mask > 0.5)
        total_positive_cls = np.sum(cls_map > 0.5)
        
        print(f"[AUTOGRAD_TARGET_DEBUG] Heatmap positive values: {total_positive_heatmap}")
        print(f"[AUTOGRAD_TARGET_DEBUG] Mask positive values: {total_positive_mask}")  
        print(f"[AUTOGRAD_TARGET_DEBUG] Classification positive values: {total_positive_cls}")
        
        # Predict potential autograd issues
        if total_positive_heatmap == 0:
            print(f"[AUTOGRAD_TARGET_WARN] ⚠️  All heatmap values are near zero - this may cause autograd issues!")
        if total_positive_mask == 0:
            print(f"[AUTOGRAD_TARGET_WARN] ⚠️  All mask values are zero - loss computation may return detached tensors!")
        if total_positive_cls == 0:
            print(f"[AUTOGRAD_TARGET_WARN] ⚠️  All classification targets are zero - classification loss may be problematic!")
            
        # Check for any completely empty sample
        if total_positive_heatmap == 0 and total_positive_mask == 0:
            print(f"[AUTOGRAD_TARGET_ERROR] ❌ CRITICAL: This sample has NO valid targets!")
            print(f"[AUTOGRAD_TARGET_ERROR] This will likely cause the 'does not require grad' error in training!")
            print(f"[AUTOGRAD_TARGET_ERROR] The head must handle this case with graph-connected dummy losses.")
        else:
            print(f"[AUTOGRAD_TARGET_DEBUG] ✓ Sample has valid targets - autograd should work correctly")
        
        print(f"[AUTOGRAD_TARGET_DEBUG] ======================================")
        
        # Add targets to results
        results['lane_targets'] = {
            'segmentation': heatmap,
            'embedding': offset,
            'height_map': z_map,
            'mask': mask,
            'gt_cls': cls_map,
        }
        
        # Add instance ID map if requested
        if self.target_config['generate_instance_ids']:
            # =============================================================================
            # CRITICAL DTYPE FIX: Ensure instance IDs are proper integer tensors
            # =============================================================================
            print(f"[INSTANCE_ID_DEBUG] === INSTANCE ID PROCESSING ===")
            print(f"[INSTANCE_ID_DEBUG] instance_ids dtype: {instance_ids.dtype}, shape: {instance_ids.shape}")
            print(f"[INSTANCE_ID_DEBUG] instance_ids unique values: {np.unique(instance_ids)}")
            print(f"[INSTANCE_ID_DEBUG] instance_ids range: [{instance_ids.min()}, {instance_ids.max()}]")
            
            # Convert to tensor with proper dtype
            instance_ids_tensor = torch.from_numpy(instance_ids).long()  # Ensure long (int64) dtype
            print(f"[INSTANCE_ID_DEBUG] After tensor conversion: dtype={instance_ids_tensor.dtype}, shape={instance_ids_tensor.shape}")
            
            results['lane_targets']['gt_instance_ids'] = instance_ids_tensor
            print(f"[INSTANCE_ID_DEBUG] Added to lane_targets with dtype: {instance_ids_tensor.dtype}")
            print(f"[INSTANCE_ID_DEBUG] ================================")
        
        # Generate visualizations for debugging
        try:
            import os
            from mmdet3d.utils.lane_visualization import debug_visualize_lane_targets
            
            # Only create visualizations if there are any lanes
            if len(gt_lanes_3d) > 0:
                point_cloud_range = [self.x_min, self.y_min, -10, self.x_max, self.y_max, 10]  # Approximate z-range
                grid_size = [self.nx, self.ny]
                
                # Create visualization directory if it doesn't exist
                save_dir = os.path.join('work_dirs', 'lane_viz')
                if not os.path.exists(save_dir):
                    os.makedirs(save_dir)
                
                # Create unique filename based on timestamp
                import time
                timestamp = int(time.time() * 1000) % 10000
                prefix = f"lane_targets_{timestamp}_"
                
                # Create visualizations
                print(f"[TARGET_GEN_DEBUG] Generating lane visualizations in {save_dir}")
                debug_visualize_lane_targets(
                    results['lane_targets'], 
                    point_cloud_range, 
                    grid_size,
                    class_names=self.lane_classes,
                    save_dir=save_dir, 
                    show=False, 
                    prefix=prefix
                )
                print(f"[TARGET_GEN_DEBUG] Visualizations saved with prefix {prefix}")
        except Exception as e:
            print(f"[TARGET_GEN_DEBUG] Error generating visualizations: {e}")
        
        # 在返回之前添加调试
        for key, value in results['lane_targets'].items():
            if isinstance(value, np.ndarray):
                print(f"[TARGET_FORMAT_DEBUG] {key} shape: {value.shape}, dtype: {value.dtype}")
            elif isinstance(value, torch.Tensor):
                print(f"[TARGET_FORMAT_DEBUG] {key} shape: {value.shape}, dtype: {value.dtype}")
        
        return results
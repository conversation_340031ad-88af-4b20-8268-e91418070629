import os
import struct
import math
from typing import Any, Dict, <PERSON><PERSON>

import mmcv
import numpy as np
import numba as nb
import torch
from nuscenes.map_expansion.map_api import NuScenesMap
from nuscenes.map_expansion.map_api import locations as LOCATIONS
from PIL import Image


from mmdet3d.core.points import BasePoints, get_points_type
from mmdet.datasets.builder import PIPELINES
from mmdet.datasets.pipelines import LoadAnnotations

from .loading_utils import load_augmented_point_cloud, reduce_LiDAR_beams
    
    
@nb.jit('u1[:,:](u1[:,:],i8[:,:],i8)', nopython=True, cache=True, parallel=False)
def numba_process_label(grid_label, sorted_label_voxel_pair, label_size):
    # label_size = len(self.classes)
    counter = np.zeros((label_size,), dtype=np.uint16)
    counter[sorted_label_voxel_pair[0, 2]] = 1
    cur_sear_ind = sorted_label_voxel_pair[0, :2]
    for i in range(1, sorted_label_voxel_pair.shape[0]):
        cur_ind = sorted_label_voxel_pair[i, :2]
        if not np.all(np.equal(cur_ind, cur_sear_ind)):
            grid_label[cur_sear_ind[0], cur_sear_ind[1]] = np.argmax(counter)
            counter = np.zeros((label_size,), dtype=np.uint16)
            cur_sear_ind = cur_ind
        counter[sorted_label_voxel_pair[i, 2]] += 1
    grid_label[cur_sear_ind[0], cur_sear_ind[1]] = np.argmax(counter)
    return grid_label


@PIPELINES.register_module()
class LoadMultiViewImageFromFiles:
    """Load multi channel images from a list of separate channel files.

    Expects results['image_paths'] to be a list of filenames.

    Args:
        to_float32 (bool): Whether to convert the img to float32.
            Defaults to False.
        color_type (str): Color type of the file. Defaults to 'unchanged'.
    """

    def __init__(self, to_float32=False, color_type="unchanged"):
        self.to_float32 = to_float32
        self.color_type = color_type

    def __call__(self, results):
        """Call function to load multi-view image from files.

        Args:
            results (dict): Result dict containing multi-view image filenames.

        Returns:
            dict: The result dict containing the multi-view image data. \
                Added keys and values are described below.

                - filename (str): Multi-view image filenames.
                - img (np.ndarray): Multi-view image arrays.
                - img_shape (tuple[int]): Shape of multi-view image arrays.
                - ori_shape (tuple[int]): Shape of original image arrays.
                - pad_shape (tuple[int]): Shape of padded image arrays.
                - scale_factor (float): Scale factor.
                - img_norm_cfg (dict): Normalization configuration of images.
        """
        filename = results.get("image_paths", [])
        if not filename:
            # Create placeholder to avoid crashing - this is a fallback
            results["image_paths"] = []
            results["img"] = []
            results["img_shape"] = []
            results["ori_shape"] = []
            results["pad_shape"] = []
            results["scale_factor"] = 1.0
            return results
            
        filename = results["image_paths"]
        # img is of shape (h, w, c, num_views)
        # modified for waymo
        images = []
        images_size = []
        default_size = (704, 256)  # Default size (width, height) if needed for placeholders
        
        for i, name in enumerate(filename):
            # Skip empty paths or handle missing files
            if not name or not os.path.exists(name):
                # Create black placeholder image
                placeholder = np.zeros((default_size[1], default_size[0], 3), dtype=np.uint8)
                img = Image.fromarray(placeholder)
            else:
                try:
                    img = Image.open(name)
                except Exception as e:
                    # Create black placeholder image
                    placeholder = np.zeros((default_size[1], default_size[0], 3), dtype=np.uint8)
                    img = Image.fromarray(placeholder)
            
            images.append(img)
            images_size.append(img.size)
        
        #TODO: consider image padding in waymo

        results["filename"] = filename
        # unravel to list, see `DefaultFormatBundle` in formating.py
        # which will transpose each image separately and then stack into array
        results["img"] = images
        # [1600, 900]
        results["img_shape"] = images_size
        results["ori_shape"] = images_size
        # Set initial values for default meta_keys
        results["pad_shape"] = images_size
        results["scale_factor"] = 1.0
        
        return results

    def __repr__(self):
        """str: Return a string that describes the module."""
        repr_str = self.__class__.__name__
        repr_str += f"(to_float32={self.to_float32}, "
        repr_str += f"color_type='{self.color_type}')"
        return repr_str

@PIPELINES.register_module()
class LoadPointsFromMultiSweeps:
    """Load points from multiple sweeps.

    This is usually used for nuScenes dataset to utilize previous sweeps.

    Args:
        sweeps_num (int): Number of sweeps. Defaults to 10.
        load_dim (int): Dimension number of the loaded points. Defaults to 5.
        use_dim (list[int]): Which dimension to use. Defaults to [0, 1, 2, 4].
        pad_empty_sweeps (bool): Whether to repeat keyframe when
            sweeps is empty. Defaults to False.
        remove_close (bool): Whether to remove close points.
            Defaults to False.
        test_mode (bool): If test_model=True used for testing, it will not
            randomly sample sweeps but select the nearest N frames.
            Defaults to False.
    """

    def __init__(
        self,
        sweeps_num=10,
        load_dim=5,
        use_dim=[0, 1, 2, 4],
        pad_empty_sweeps=False,
        remove_close=False,
        test_mode=False,
        load_augmented=None,
        reduce_beams=None,
    ):
        self.load_dim = load_dim
        self.sweeps_num = sweeps_num
        if isinstance(use_dim, int):
            use_dim = list(range(use_dim))
        self.use_dim = use_dim
        self.pad_empty_sweeps = pad_empty_sweeps
        self.remove_close = remove_close
        self.test_mode = test_mode
        self.load_augmented = load_augmented
        self.reduce_beams = reduce_beams

    def _load_points(self, lidar_path):
        """Private function to load point clouds data.

        Args:
            lidar_path (str): Filename of point clouds data.

        Returns:
            np.ndarray: An array containing point clouds data.
        """
        mmcv.check_file_exist(lidar_path)
        if self.load_augmented:
            assert self.load_augmented in ["pointpainting", "mvp"]
            virtual = self.load_augmented == "mvp"
            points = load_augmented_point_cloud(
                lidar_path, virtual=virtual, reduce_beams=self.reduce_beams
            )
        elif lidar_path.endswith(".npy"):
            points = np.load(lidar_path)
        else:
            points = np.fromfile(lidar_path, dtype=np.float32)
        return points

    def _remove_close(self, points, radius=1.0):
        """Removes point too close within a certain radius from origin.

        Args:
            points (np.ndarray | :obj:`BasePoints`): Sweep points.
            radius (float): Radius below which points are removed.
                Defaults to 1.0.

        Returns:
            np.ndarray: Points after removing.
        """
        if isinstance(points, np.ndarray):
            points_numpy = points
        elif isinstance(points, BasePoints):
            points_numpy = points.tensor.numpy()
        else:
            raise NotImplementedError
        x_filt = np.abs(points_numpy[:, 0]) < radius
        y_filt = np.abs(points_numpy[:, 1]) < radius
        not_close = np.logical_not(np.logical_and(x_filt, y_filt))
        return points[not_close]

    def __call__(self, results):
        """Call function to load multi-sweep point clouds from files.

        Args:
            results (dict): Result dict containing multi-sweep point cloud \
                filenames.

        Returns:
            dict: The result dict containing the multi-sweep points data. \
                Added key and value are described below.

                - points (np.ndarray | :obj:`BasePoints`): Multi-sweep point \
                    cloud arrays.
        """
        points = results["points"]
        points.tensor[:, 4] = 0
        sweep_points_list = [points]
        ts = results["timestamp"] / 1e6
        if self.pad_empty_sweeps and len(results["sweeps"]) == 0:
            for i in range(self.sweeps_num):
                if self.remove_close:
                    sweep_points_list.append(self._remove_close(points))
                else:
                    sweep_points_list.append(points)
        else:
            if len(results["sweeps"]) <= self.sweeps_num:
                choices = np.arange(len(results["sweeps"]))
            elif self.test_mode:
                choices = np.arange(self.sweeps_num)
            else:
                # NOTE: seems possible to load frame -11?
                if not self.load_augmented:
                    choices = np.random.choice(
                        len(results["sweeps"]), self.sweeps_num, replace=False
                    )
                else:
                    # don't allow to sample the earliest frame, match with Tianwei's implementation.
                    choices = np.random.choice(
                        len(results["sweeps"]) - 1, self.sweeps_num, replace=False
                    )
            for idx in choices:
                sweep = results["sweeps"][idx]
                points_sweep = self._load_points(sweep["data_path"])
                points_sweep = np.copy(points_sweep).reshape(-1, self.load_dim)

                # TODO: make it more general
                if self.reduce_beams and self.reduce_beams < 32:
                    points_sweep = reduce_LiDAR_beams(points_sweep, self.reduce_beams)

                if self.remove_close:
                    points_sweep = self._remove_close(points_sweep)
                sweep_ts = sweep["timestamp"] / 1e6
                points_sweep[:, :3] = (
                    points_sweep[:, :3] @ sweep["sensor2lidar_rotation"].T
                )
                points_sweep[:, :3] += sweep["sensor2lidar_translation"]
                points_sweep[:, 4] = ts - sweep_ts
                points_sweep = points.new_point(points_sweep)
                sweep_points_list.append(points_sweep)

        points = points.cat(sweep_points_list)
        points = points[:, self.use_dim]
        results["points"] = points
        return results

    def __repr__(self):
        """str: Return a string that describes the module."""
        return f"{self.__class__.__name__}(sweeps_num={self.sweeps_num})"

@PIPELINES.register_module()
class LoadBEVSegmentation:
    def __init__(
        self,
        dataset_root: str,
        # xbound: Tuple[float, float, float],
        # ybound: Tuple[float, float, float],
        scale: Tuple[float, float, float, float, float, float],
        resolution: Tuple[float, float, float],
        classes: Tuple[str, ...],
        ignore_label: int,
        downsample_rate=8,
    ) -> None:
        super().__init__()
        self.scale = scale
        self.resolution = resolution 
        self.downsample_rate = downsample_rate
        dim_x = round((scale[3] - scale[0]) / resolution[0] / self.downsample_rate)  # int((max-min)/interval)
        dim_y = round((scale[4] - scale[1]) / resolution[1] / self.downsample_rate)  
        dim_z = round((scale[5] - scale[2]) / resolution[2] / self.downsample_rate) 
        self.voxel_size = (dim_x, dim_y, dim_z)
        self.classes = classes
        self.ignore_label = ignore_label


    def __call__(self, data: Dict[str, Any]) -> Dict[str, Any]:
        '''
        lidar2point = data["lidar_aug_matrix"]
        point2lidar = np.linalg.inv(lidar2point)
        lidar2ego = data["lidar2ego"]
        ego2global = data["ego2global"]
        lidar2global = ego2global @ lidar2ego @ point2lidar

        map_pose = lidar2global[:2, 3]
        patch_box = (map_pose[0], map_pose[1], self.patch_size[0], self.patch_size[1])

        rotation = lidar2global[:3, :3]
        v = np.dot(rotation, np.array([1, 0, 0]))
        yaw = np.arctan2(v[1], v[0])
        patch_angle = yaw / np.pi * 180

        mappings = {}
        for name in self.classes:
            if name == "drivable_area*":
                mappings[name] = ["road_segment", "lane"]
            elif name == "divider":
                mappings[name] = ["road_divider", "lane_divider"]
            else:
                mappings[name] = [name]

        layer_names = []
        for name in mappings:
            layer_names.extend(mappings[name])
        layer_names = list(set(layer_names))

        location = data["location"]
        masks = self.maps[location].get_map_mask(
            patch_box=patch_box,
            patch_angle=patch_angle,
            layer_names=layer_names,
            canvas_size=self.canvas_size,
        ) # np.array
        # masks = masks[:, ::-1, :].copy()
        masks = masks.transpose(0, 2, 1)
        masks = masks.astype(np.bool)

        num_classes = len(self.classes)
        labels = np.zeros((num_classes, *self.canvas_size), dtype=np.long)
        for k, name in enumerate(self.classes):
            for layer_name in mappings[name]:
                index = layer_names.index(layer_name)
                labels[k, masks[index]] = 1

        '''
        # temp set all 0
        # labels = np.zeros((len(self.classes), *self.voxel_size[:2]), dtype=np.long)
        # data["gt_masks_bev"] = labels
        points = data['points'].tensor.numpy()[:,:2].astype(np.float32)
        grid_ind = (np.floor((np.clip(points, self.scale[:2], [x-0.001 for x in self.scale[3:5]]) - self.scale[:2]) 
                             / (np.array(self.resolution[:2]) * self.downsample_rate))).astype(np.int)
        grid_label = np.ones(self.voxel_size[:2], dtype=np.uint8) * self.ignore_label
        if "points_labels" in data:
            label_voxel_pair = np.concatenate([grid_ind, data['points_labels'].reshape(-1,1)], axis=1)
            label_voxel_pair = label_voxel_pair[np.lexsort((grid_ind[:, 0], grid_ind[:, 1])), :]
            grid_label = numba_process_label(np.copy(grid_label), label_voxel_pair, len(self.classes))
        data['gt_masks_bev'] = grid_label

        data['points_grid_ind'] = grid_ind
        return data
    

@PIPELINES.register_module()
class LoadPointsFromFile:
    """Load Points From File.

    Load sunrgbd and scannet points from file.

    Args:
        coord_type (str): The type of coordinates of points cloud.
            Available options includes:
            - 'LIDAR': Points in LiDAR coordinates.
            - 'DEPTH': Points in depth coordinates, usually for indoor dataset.
            - 'CAMERA': Points in camera coordinates.
        load_dim (int): The dimension of the loaded points.
            Defaults to 6.
        use_dim (list[int]): Which dimensions of the points to be used.
            Defaults to [0, 1, 2]. For KITTI dataset, set use_dim=4
            or use_dim=[0, 1, 2, 3] to use the intensity dimension.
        shift_height (bool): Whether to use shifted height. Defaults to False.
        use_color (bool): Whether to use color features. Defaults to False.
    """

    def __init__(
        self,
        coord_type,
        load_dim=6,
        use_dim=[0, 1, 2],
        shift_height=False,
        use_color=False,
        load_augmented=None,
        reduce_beams=None,
        label_mapping=None,
        its_mean=[0.0],
        its_std=[256.0],
        with_lidarid=False
    ):
        self.shift_height = shift_height
        self.use_color = use_color
        if isinstance(use_dim, int):
            use_dim = list(range(use_dim))
        assert (
            max(use_dim) < load_dim
        ), f"Expect all used dimensions < {load_dim}, got {use_dim}"
        assert coord_type in ["CAMERA", "LIDAR", "DEPTH"]

        self.coord_type = coord_type
        self.load_dim = load_dim
        self.use_dim = use_dim
        self.load_augmented = load_augmented
        self.reduce_beams = reduce_beams
        self.label_mapping = label_mapping
        self.its_mean=its_mean
        self.its_std=its_std
        self.with_lidarid=with_lidarid

    def _parse_pcd_header(self, f):
        """Parse PCD file header to extract format and field information.
        
        Args:
            f: Binary file object opened for the PCD file
            
        Returns:
            dict: Header information including data_format and fields
        """
        header_info = {
            'data_format': None,
            'fields': [],
            'size': [],
            'type': [],
            'count': [],
            'width': 0,
            'height': 0,
            'points': 0
        }
        
        line_count = 0
        data_start_pos = None
        
        while True:
            pos = f.tell()
            line = f.readline()
            line_count += 1
            
            if not line:
                break
                
            try:
                line = line.decode('utf-8').strip()
            except UnicodeDecodeError:
                # If we can't decode, we might have hit binary data
                f.seek(pos)
                break
                
            if line.startswith('DATA'):
                if 'ascii' in line:
                    header_info['data_format'] = 'ascii'
                elif 'binary' in line:
                    header_info['data_format'] = 'binary'
                    data_start_pos = f.tell()
                else:
                    raise ValueError(f"Unknown DATA format: {line}")
                break
            elif line.startswith('FIELDS'):
                header_info['fields'] = line.split()[1:]
            elif line.startswith('SIZE'):
                header_info['size'] = [int(x) for x in line.split()[1:]]
            elif line.startswith('TYPE'):
                header_info['type'] = line.split()[1:]
            elif line.startswith('COUNT'):
                header_info['count'] = [int(x) for x in line.split()[1:]]
            elif line.startswith('WIDTH'):
                header_info['width'] = int(line.split()[1])
            elif line.startswith('HEIGHT'):
                header_info['height'] = int(line.split()[1])
            elif line.startswith('POINTS'):
                header_info['points'] = int(line.split()[1])
            
            # Safety check to avoid infinite loops
            if line_count > 20:
                break
        
        # If binary format, ensure we know where data starts
        if header_info['data_format'] == 'binary' and data_start_pos is not None:
            header_info['data_start_pos'] = data_start_pos
            
        return header_info

    def _read_pcd_binary(self, f, header_info):
        """Read binary PCD format data.
        
        Args:
            f: Binary file object
            header_info: Header information from _parse_pcd_header
            
        Returns:
            list: List of point data
        """
        points_list = []
        
        # Get field information
        fields = header_info.get('fields', ['x', 'y', 'z', 'intensity'])
        sizes = header_info.get('size', [4, 4, 4, 4])  # Default to float32
        types = header_info.get('type', ['F', 'F', 'F', 'F'])  # Default to float
        
        # Calculate bytes per point
        bytes_per_point = sum(sizes)
        
        # Determine struct format for unpacking
        format_chars = []
        for i, (field, size, type_char) in enumerate(zip(fields, sizes, types)):
            if type_char == 'F':  # Float
                if size == 4:
                    format_chars.append('f')  # float32
                elif size == 8:
                    format_chars.append('d')  # float64
            elif type_char == 'I':  # Integer
                if size == 1:
                    format_chars.append('B')  # uint8
                elif size == 2:
                    format_chars.append('H')  # uint16
                elif size == 4:
                    format_chars.append('I')  # uint32
            elif type_char == 'U':  # Unsigned integer
                if size == 1:
                    format_chars.append('B')  # uint8
                elif size == 2:
                    format_chars.append('H')  # uint16
                elif size == 4:
                    format_chars.append('I')  # uint32
            else:
                # Default to float32
                format_chars.append('f')
        
        struct_format = '<' + ''.join(format_chars)  # Little endian
        
        try:
            while True:
                data = f.read(bytes_per_point)
                if len(data) != bytes_per_point:
                    break
                
                try:
                    # Unpack the binary data
                    unpacked = struct.unpack(struct_format, data)
                    
                    # Check for NaN values and skip if found
                    if any(math.isnan(val) if isinstance(val, float) else False for val in unpacked):
                        continue
                        
                    # Convert to standard format (x, y, z, intensity, ...)
                    point = list(unpacked)
                    points_list.append(point)
                    
                except struct.error:
                    # Skip corrupted data points
                    continue
                    
        except Exception as e:
            print(f"Warning: Error reading binary PCD data: {e}")
            
        return points_list

    def _load_points(self, lidar_path):
        """Private function to load point clouds data.

        Args:
            lidar_path (str): Filename of point clouds data.

        Returns:
            tuple: (points, points_labels) where points is a numpy array and 
                  points_labels is either a numpy array or an empty list.
        """
        mmcv.check_file_exist(lidar_path)
        points_labels = []
        if self.load_augmented:
            assert self.load_augmented in ["pointpainting", "mvp"]
            virtual = self.load_augmented == "mvp"
            points = load_augmented_point_cloud(
                lidar_path, virtual=virtual, reduce_beams=self.reduce_beams
            )
        elif lidar_path.endswith(".npy"):
            points = np.load(lidar_path)
        elif lidar_path.endswith(".npz"):
            npz = np.load(lidar_path)
            points = npz['pcds']
            if 'pcds_label' in npz:
                points_labels = npz['pcds_label']
                # 确保点云和标签的数量一致
                if points.shape[0] != points_labels.shape[0]:
                    print(f"Warning: points shape {points.shape[0]} != labels shape {points_labels.shape[0]}")
                    # 如果不一致，不使用标签
                    points_labels = []
        elif lidar_path.endswith(".pcd"):
            # Read PCD file - support both ASCII and binary formats
            points_list = []
            
            # First, detect the format by reading the header
            with open(lidar_path, 'rb') as f:
                # Read header to determine format and extract metadata
                header_info = self._parse_pcd_header(f)
                data_format = header_info['data_format']
                fields = header_info['fields']
                
                if data_format == 'ascii':
                    # ASCII format - reopen in text mode
                    f.close()
                    with open(lidar_path, 'r') as text_f:
                        # Skip header until DATA line
                        line = text_f.readline()
                        while line.strip() != 'DATA ascii':
                            line = text_f.readline()
                            if not line:
                                raise ValueError("Invalid PCD file: no DATA ascii marker found")
                        
                        # Read point cloud data
                        for line in text_f:
                            line = line.strip()
                            if line:  # Skip empty lines
                                # Split line into float values (x, y, z, intensity, etc.)
                                point = [float(x) for x in line.split()]
                                points_list.append(point)
                
                elif data_format == 'binary':
                    # Binary format
                    points_list = self._read_pcd_binary(f, header_info)
                
                else:
                    raise ValueError(f"Unsupported PCD data format: {data_format}")
            
            # Convert to numpy array
            points = np.array(points_list, dtype=np.float32)
        else:
            points = np.fromfile(lidar_path, dtype=np.float32)

        if self.with_lidarid:
            lidarid = points[:, 4].copy()
            intensity = points[:, 3].copy()
            for idx in range(len(self.its_mean)):
                mean_idx = self.its_mean[idx]
                std_idx = self.its_std[idx]
                mask = (lidarid==idx)
                intensity[mask] = (intensity[mask]-mean_idx)/std_idx
                points[:, 3] = intensity
        else:
            points[:, 3] = (points[:, 3]-self.its_mean[0])/self.its_std[0]
        return points, points_labels

    def __call__(self, results):
        """Call function to load points data from file.

        Args:
            results (dict): Result dict containing point clouds data.

        Returns:
            dict: The result dict containing the point clouds data. \
                Added key and value are described below.

                - points (:obj:`BasePoints`): Point clouds data.
        """
        lidar_path = results["lidar_path"]
        points, points_labels = self._load_points(lidar_path)
        points = points.reshape(-1, self.load_dim)
        # TODO: make it more general
        if self.reduce_beams and self.reduce_beams < 32:
            points = reduce_LiDAR_beams(points, self.reduce_beams)
        points = points[:, self.use_dim]
        attribute_dims = None

        if self.shift_height:
            floor_height = np.percentile(points[:, 2], 0.99)
            height = points[:, 2] - floor_height
            points = np.concatenate(
                [points[:, :3], np.expand_dims(height, 1), points[:, 3:]], 1
            )
            attribute_dims = dict(height=3)

        if self.use_color:
            assert len(self.use_dim) >= 6
            if attribute_dims is None:
                attribute_dims = dict()
            attribute_dims.update(
                dict(
                    color=[
                        points.shape[1] - 3,
                        points.shape[1] - 2,
                        points.shape[1] - 1,
                    ]
                )
            )

        # Debug point cloud dimensions
        print(f"[POINTS_DEBUG] Loaded points shape: {points.shape}, use_dim: {self.use_dim}")
        if points.shape[0] > 0:
            print(f"[POINTS_DEBUG] Point ranges - X: [{points[:, 0].min():.2f}, {points[:, 0].max():.2f}], Y: [{points[:, 1].min():.2f}, {points[:, 1].max():.2f}], Z: [{points[:, 2].min():.2f}, {points[:, 2].max():.2f}]")
        
        points_class = get_points_type(self.coord_type)
        points = points_class(
            points, points_dim=points.shape[-1], attribute_dims=attribute_dims
        )
        results["points"] = points

        # 处理点云标签，需要检查points_labels和label_mapping是否存在
        # 首先检查points_labels是否为空，以及是否为numpy数组
        if isinstance(points_labels, np.ndarray) and points_labels.size > 0:
            # 有标签，检查是否需要进行标签映射
            if 'label_mapping' in results and 'learning_map' in results['label_mapping']:
                try:
                    points_labels = np.vectorize(results['label_mapping']['learning_map'].__getitem__)(points_labels)
                    results["points_labels"] = points_labels
                except Exception as e:
                    print(f"Warning: Failed to process points_labels: {e}")
            else:
                # 直接使用原始标签
                results["points_labels"] = points_labels
        elif isinstance(points_labels, list) and len(points_labels) > 0:
            # 如果是非空列表，先转为numpy数组
            try:
                points_labels_array = np.array(points_labels)
                if 'label_mapping' in results and 'learning_map' in results['label_mapping']:
                    points_labels_array = np.vectorize(results['label_mapping']['learning_map'].__getitem__)(points_labels_array)
                results["points_labels"] = points_labels_array
            except Exception as e:
                print(f"Warning: Failed to convert list points_labels to array: {e}")

        return results


@PIPELINES.register_module()
class LoadAnnotations3D(LoadAnnotations):
    """Load Annotations3D.

    Load instance mask and semantic mask of points and
    encapsulate the items into related fields.

    Args:
        with_bbox_3d (bool, optional): Whether to load 3D boxes.
            Defaults to True.
        with_label_3d (bool, optional): Whether to load 3D labels.
            Defaults to True.
        with_attr_label (bool, optional): Whether to load attribute label.
            Defaults to False.
        with_bbox (bool, optional): Whether to load 2D boxes.
            Defaults to False.
        with_label (bool, optional): Whether to load 2D labels.
            Defaults to False.
        with_mask (bool, optional): Whether to load 2D instance masks.
            Defaults to False.
        with_seg (bool, optional): Whether to load 2D semantic masks.
            Defaults to False.
        with_bbox_depth (bool, optional): Whether to load 2.5D boxes.
            Defaults to False.
        poly2mask (bool, optional): Whether to convert polygon annotations
            to bitmasks. Defaults to True.
    """

    def __init__(
        self,
        with_bbox_3d=True,
        with_label_3d=True,
        with_attr_label=False,
        with_bbox=False,
        with_label=False,
        with_mask=False,
        with_seg=False,
        with_bbox_depth=False,
        poly2mask=True,
    ):
        super().__init__(
            with_bbox,
            with_label,
            with_mask,
            with_seg,
            poly2mask,
        )
        self.with_bbox_3d = with_bbox_3d
        self.with_bbox_depth = with_bbox_depth
        self.with_label_3d = with_label_3d
        self.with_attr_label = with_attr_label

    def _load_bboxes_3d(self, results):
        """Private function to load 3D bounding box annotations.

        Args:
            results (dict): Result dict from :obj:`mmdet3d.CustomDataset`.

        Returns:
            dict: The dict containing loaded 3D bounding box annotations.
        """
        results["gt_bboxes_3d"] = results["ann_info"]["gt_bboxes_3d"]
        results["bbox3d_fields"].append("gt_bboxes_3d")
        return results

    def _load_bboxes_depth(self, results):
        """Private function to load 2.5D bounding box annotations.

        Args:
            results (dict): Result dict from :obj:`mmdet3d.CustomDataset`.

        Returns:
            dict: The dict containing loaded 2.5D bounding box annotations.
        """
        results["centers2d"] = results["ann_info"]["centers2d"]
        results["depths"] = results["ann_info"]["depths"]
        return results

    def _load_labels_3d(self, results):
        """Private function to load label annotations.

        Args:
            results (dict): Result dict from :obj:`mmdet3d.CustomDataset`.

        Returns:
            dict: The dict containing loaded label annotations.
        """
        results["gt_labels_3d"] = results["ann_info"]["gt_labels_3d"]
        return results

    def _load_attr_labels(self, results):
        """Private function to load label annotations.

        Args:
            results (dict): Result dict from :obj:`mmdet3d.CustomDataset`.

        Returns:
            dict: The dict containing loaded label annotations.
        """
        results["attr_labels"] = results["ann_info"]["attr_labels"]
        return results

    def __call__(self, results):
        """Call function to load multiple types annotations.

        Args:
            results (dict): Result dict from :obj:`mmdet3d.CustomDataset`.

        Returns:
            dict: The dict containing loaded 3D bounding box, label, mask and
                semantic segmentation annotations.
        """
        results = super().__call__(results)
        if self.with_bbox_3d:
            results = self._load_bboxes_3d(results)
            if results is None:
                return None
        if self.with_bbox_depth:
            results = self._load_bboxes_depth(results)
            if results is None:
                return None
        if self.with_label_3d:
            results = self._load_labels_3d(results)
        if self.with_attr_label:
            results = self._load_attr_labels(results)

        return results


@PIPELINES.register_module()
class LoadLaneAnnotations3D(object):
    """Load lane annotations for 3D lane detection.

    Args:
        with_lane_3d (bool): Whether to load 3D lane annotations.
            Default: True.
        with_lane_label_3d (bool): Whether to load 3D lane labels.
            Default: True.
        with_lane_attr (bool): Whether to load lane attributes.
            Default: False.
        with_lane_uv (bool): Whether to load lane UV image coordinates.
            Default: False.
    """

    def __init__(self,
                 with_lane_3d=True,
                 with_lane_label_3d=True,
                 with_lane_attr=False,
                 with_lane_uv=False):
        self.with_lane_3d = with_lane_3d
        self.with_lane_label_3d = with_lane_label_3d
        self.with_lane_attr = with_lane_attr
        self.with_lane_uv = with_lane_uv

    def __call__(self, results):
        """Call function to load lane annotations.

        Args:
            results (dict): Result dict from :obj:`mmdet3d.CustomDataset`.

        Returns:
            dict: The dict containing loaded 3D lane annotations.
        """
        print("[LOADING_LANES] Starting lane annotation loading")
        if 'ann_info' not in results:
            print("[LOADING_LANES] No ann_info in results, setting empty lane annotations")
            results['gt_lanes_3d'] = []
            results['gt_lane_labels'] = []
            if self.with_lane_uv:
                results['gt_lane_points_uvs'] = []
            return results

        ann_info = results['ann_info']
        
        # Load 3D lanes
        if self.with_lane_3d and 'gt_lanes_3d' in ann_info:
            gt_lanes_3d = ann_info['gt_lanes_3d']
            
            if len(gt_lanes_3d) > 0:
                # Check if lanes are within point_cloud_range
                if 'point_cloud_range' in results:
                    pc_range = results['point_cloud_range']
                    if pc_range is not None:
                        x_min, y_min, z_min, x_max, y_max, z_max = pc_range
                        lanes_in_range = 0
                        
                        for lane_idx, lane in enumerate(gt_lanes_3d):
                            lane_array = np.array(lane)
                            lane_x = lane_array[:, 0]
                            lane_y = lane_array[:, 1]
                            lane_z = lane_array[:, 2]
                            
                            # Check if any point is within range
                            in_x_range = np.logical_and(lane_x >= x_min, lane_x <= x_max)
                            in_y_range = np.logical_and(lane_y >= y_min, lane_y <= y_max)
                            in_z_range = np.logical_and(lane_z >= z_min, lane_z <= z_max)
                            in_range = np.logical_and(in_x_range, np.logical_and(in_y_range, in_z_range))
                            
                            points_in_range = np.sum(in_range)
                            if points_in_range > 0:
                                lanes_in_range += 1
                            
                            print(f"[LOADING_LANES] Lane {lane_idx}: {points_in_range}/{len(lane)} points in range")
                            print(f"[LOADING_LANES] Lane {lane_idx} ranges - X: [{np.min(lane_x):.2f}, {np.max(lane_x):.2f}], Y: [{np.min(lane_y):.2f}, {np.max(lane_y):.2f}], Z: [{np.min(lane_z):.2f}, {np.max(lane_z):.2f}]")
                        
                        print(f"[LOADING_LANES] {lanes_in_range}/{len(gt_lanes_3d)} lanes have points within point_cloud_range {pc_range}")
                else:
                    print("[LOADING_LANES] point_cloud_range not available, skipping range check")
                
                # Print overall lane statistics
                total_points = sum(len(lane) for lane in gt_lanes_3d)
                print(f"[LOADING_LANES] Loaded {len(gt_lanes_3d)} lanes with total {total_points} points")
            else:
                print("[LOADING_LANES] No lanes found in annotations")
            
            results['gt_lanes_3d'] = gt_lanes_3d
        else:
            print("[LOADING_LANES] gt_lanes_3d not available or not requested")
            results['gt_lanes_3d'] = []

        # Load lane labels
        if self.with_lane_label_3d and 'gt_lane_labels' in ann_info:
            gt_lane_labels = ann_info['gt_lane_labels']
            results['gt_lane_labels'] = gt_lane_labels
            
            if len(gt_lane_labels) > 0:
                print(f"[LOADING_LANES] Loaded lane labels: {gt_lane_labels}")
                
                # Verify that number of labels matches number of lanes
                if len(results['gt_lanes_3d']) != len(gt_lane_labels):
                    print(f"[LOADING_LANES] WARNING: Number of lanes ({len(results['gt_lanes_3d'])}) doesn't match number of labels ({len(gt_lane_labels)})")
            else:
                print("[LOADING_LANES] No lane labels found in annotations")
        else:
            print("[LOADING_LANES] gt_lane_labels not available or not requested")
            results['gt_lane_labels'] = []

        # Load lane UV points (optional)
        if self.with_lane_uv and 'gt_lane_points_uvs' in ann_info:
            results['gt_lane_points_uvs'] = ann_info['gt_lane_points_uvs']
        
        return results

    def __repr__(self):
        """str: Return a string that describes the module."""
        repr_str = self.__class__.__name__
        repr_str += f'(with_lane_3d={self.with_lane_3d}, '
        repr_str += f'with_lane_label_3d={self.with_lane_label_3d})'
        return repr_str


@PIPELINES.register_module()
class LoadDenseDepthMapFromFile(object):
    """Load dense depth map from file.

    Args:
        to_float32 (bool): Whether to convert the img to float32.
            Defaults to True.
        color_type (str): Color type of the file. Defaults to 'unchanged'.
        depth_scale (float): Depth scale factor for PENET format.
            PENET uses PNG_value = depth_meters × 256, so depth_scale=256.0.
            Defaults to 256.0.
        default_depth (float): Default depth value for missing regions.
            Defaults to 0.0.
        max_depth (float): Maximum valid depth in meters. Values above this
            will be clamped. Defaults to 80.0.
        min_depth (float): Minimum valid depth in meters. Values below this
            will be set to 0. Defaults to 0.1.
    """

    def __init__(self,
                 to_float32=True,
                 color_type='unchanged',
                 depth_scale=256.0,
                 default_depth=0.0,
                 max_depth=80.0,
                 min_depth=0.1):
        self.to_float32 = to_float32
        self.color_type = color_type
        self.depth_scale = depth_scale
        self.default_depth = default_depth
        self.max_depth = max_depth
        self.min_depth = min_depth

    def __call__(self, results):
        """Call function to load dense depth map.

        Args:
            results (dict): Result dict containing dense depth map path.

        Returns:
            dict: The result dict containing the dense depth map.
                Added keys and values are described below.

                - depth_map (ndarray): Dense depth map.
                - depth_map_valid (ndarray): Validity mask for depth map.
        """
        # 简化：直接使用cam_list的第一个相机作为前视相机
        depth_map_path = self._get_front_camera_depth_path(results)
        
        if depth_map_path and os.path.exists(depth_map_path):
            try:
                # Load 16-bit PNG depth map using PIL for proper uint16 handling
                from PIL import Image
                import numpy as np
                
                # Load as 16-bit grayscale
                depth_img = Image.open(depth_map_path)
                
                # Convert to numpy array, preserving 16-bit depth
                if depth_img.mode == 'I;16':
                    # 16-bit grayscale mode
                    depth_map = np.array(depth_img, dtype=np.uint16)
                elif depth_img.mode == 'I':
                    # 32-bit integer mode, convert to uint16
                    depth_array = np.array(depth_img, dtype=np.int32)
                    depth_map = np.clip(depth_array, 0, 65535).astype(np.uint16)
                elif depth_img.mode == 'L':
                    # 8-bit grayscale, extend to 16-bit
                    depth_map = np.array(depth_img, dtype=np.uint16)
                    depth_map = depth_map * 256  # Scale to 16-bit range
                else:
                    # Convert to grayscale if multi-channel
                    if len(depth_img.split()) > 1:
                        depth_img = depth_img.convert('L')
                    depth_map = np.array(depth_img, dtype=np.uint16)
                
                # Decode PENET format: PNG_value = depth_meters × 256
                # So depth_meters = PNG_value / 256
                if self.to_float32:
                    depth_map = depth_map.astype(np.float32) / self.depth_scale
                else:
                    depth_map = depth_map / self.depth_scale
                
                # Apply depth range filtering
                depth_map = np.clip(depth_map, 0, self.max_depth)
                
                # Create validity mask
                depth_map_valid = (depth_map >= self.min_depth).astype(np.float32)
                
                # Replace invalid values with default depth
                if self.default_depth > 0:
                    depth_map[depth_map < self.min_depth] = self.default_depth
                
                # Ensure single channel (remove any extra dimensions)
                if len(depth_map.shape) > 2:
                    depth_map = depth_map[:, :, 0] if depth_map.shape[2] == 1 else depth_map.mean(axis=2)
                    
                print(f"Successfully loaded depth map from {depth_map_path}")
                print(f"Depth map shape: {depth_map.shape}, dtype: {depth_map.dtype}")
                print(f"Depth range: [{depth_map.min():.3f}, {depth_map.max():.3f}] meters")
                print(f"Valid pixels: {depth_map_valid.sum()}/{depth_map_valid.size} ({100*depth_map_valid.mean():.1f}%)")
                
            except Exception as e:
                print(f"Error loading depth map from {depth_map_path}: {e}")
                # Use dummy depth map on error
                depth_map = np.full((256, 704), self.default_depth, dtype=np.float32)
                depth_map_valid = np.zeros((256, 704), dtype=np.float32)
        else:
            print(f"No valid depth map path found: {depth_map_path}")
            # Use dummy depth map if no path provided
            depth_map = np.full((256, 704), self.default_depth, dtype=np.float32)
            depth_map_valid = np.zeros((256, 704), dtype=np.float32)

        # Add depth map to results
        results['depth_map'] = depth_map
        results['depth_map_valid'] = depth_map_valid
        print(f"*************results['depth_map']: {results['depth_map']}")
        print(f"*************results['depth_map_valid']: {results['depth_map_valid']}")
        
        return results

    def _get_front_camera_depth_path(self, results):
        """Get depth map path for front camera, prioritizing '120_front'.
        
        适配7相机设置: ['60_front', '120_front', '120_left', '120_right', '120_back', 'right_back', 'left_back']
        优先寻找'120_front'相机，如果不存在则尝试其他前视相机
        """
        if 'image_paths' not in results or not results['image_paths']:
            return None
        
        # 从dataset中获取cam_list
        cam_list = getattr(results, 'cam_list', None)
        if not cam_list and hasattr(results, 'dataset_root'):
            # 如果无法获取cam_list，使用默认值
            cam_list = ['60_front', '120_front', '120_left', '120_right', '120_back', 'right_back', 'left_back']
        
        print(f"[LoadDenseDepthMapFromFile] Available cameras: {len(results['image_paths']) if 'image_paths' in results else 0}")
        print(f"[LoadDenseDepthMapFromFile] Camera list: {cam_list}")
        
        # 前视相机优先级: 120_front > 60_front > 其他
        front_cam_priorities = ['120_front', '60_front']
        
        front_cam_path = None
        front_cam_index = None
        
        # 按优先级寻找前视相机
        for priority_cam in front_cam_priorities:
            if cam_list and priority_cam in cam_list:
                cam_index = cam_list.index(priority_cam)
                if cam_index < len(results['image_paths']) and results['image_paths'][cam_index]:
                    front_cam_path = results['image_paths'][cam_index]
                    front_cam_index = cam_index
                    break
        
        # 如果没有找到优先相机，使用第一个可用的相机
        if not front_cam_path:
            for i, path in enumerate(results['image_paths']):
                if path:
                    front_cam_path = path
                    front_cam_index = i
                    break
                    
        if not front_cam_path:
            return None
            
        # 从图像路径构建深度图路径
        # # 例如: /path/to/segment_01/120_front/frame.jpg -> /path/to/segment_01/depth_maps/frame.png
        # img_dir = os.path.dirname(front_cam_path)
        # segment_dir = os.path.dirname(os.path.dirname(img_dir))
        # frame_name = os.path.splitext(os.path.basename(front_cam_path))[0]
        # print(f"----------------img_dir", img_dir)
        # print(f"----------------segment_dir", segment_dir)
        # print(f"----------------frame_name", frame_name)
        
        # depth_path = os.path.join(segment_dir, 'depth_maps', f"{frame_name}.png")
        # print(f"----------------depth_path", depth_path)
        
        # return depth_path if os.path.exists(depth_path) else None
        
        
        # 例如: /path/to/segment_01/images/120_front/frame.jpg -> /path/to/segment_01/depth_maps/frame.png
        try:
            # Parse path structure: .../segment_id/images/120_front/frame.jpg
            path_parts = front_cam_path.split(os.sep)
            
            # Find the 'images' directory in the path
            images_idx = None
            for i, part in enumerate(path_parts):
                if part == 'images':
                    images_idx = i
                    break
            
            if images_idx is None:
                # Fallback: assume structure .../images/120_front/frame.jpg
                img_dir = os.path.dirname(front_cam_path)
                segment_dir = os.path.dirname(os.path.dirname(img_dir))
            else:
                # Reconstruct segment directory path
                segment_parts = path_parts[:images_idx]
                segment_dir = os.sep.join(segment_parts)
            
            frame_name = os.path.splitext(os.path.basename(front_cam_path))[0]
            depth_path = os.path.join(segment_dir, 'depth_maps', f"{frame_name}.png")
            
            return depth_path if os.path.exists(depth_path) else None
            
        except Exception as e:
            print(f"Error constructing depth map path from {front_cam_path}: {e}")
            return None

@PIPELINES.register_module()
class GenerateBEVLaneTargets(object):
    """Generate BEV lane targets for anchor-based lane detection.
    
    Args:
        point_cloud_range (list): Range of BEV [x_min, y_min, z_min, x_max, y_max, z_max].
        voxel_size (list): Size of voxels [x_size, y_size, z_size].
        grid_size (list): Size of BEV grid [x_size, y_size].
        lane_classes (list): List of lane class names.
        target_config (dict): Configuration for generating targets.
            - use_segmentation_mask (bool): Whether to use segmentation mask. Default: True.
            - use_instance_embedding (bool): Whether to use instance embedding. Default: True.
            - use_z_height (bool): Whether to use z height information. Default: True.
            - use_xy_offset (bool): Whether to use xy offsets. Default: False.
            - segmentation_ignore_index (int): Ignore index for segmentation. Default: 0.
            - line_width (int): Width of lines in segmentation mask. Default: 3.
            - embed_dim (int): Dimension of embedding. Default: 4.
    """
    
    def __init__(self,
                 point_cloud_range,
                 voxel_size,
                 grid_size,
                 lane_classes,
                 target_config=None):
        self.point_cloud_range = np.array(point_cloud_range)
        self.voxel_size = np.array(voxel_size)
        self.grid_size = np.array(grid_size)
        self.lane_classes = lane_classes
        
        # Default target configuration
        self.target_config = {
            'use_segmentation_mask': True,
            'use_instance_embedding': True,
            'use_z_height': True,
            'use_xy_offset': False,
            'segmentation_ignore_index': 0,
            'line_width': 5,
            'embed_dim': 4,
        }
        
        # Update with provided configuration
        if target_config is not None:
            self.target_config.update(target_config)
        
        # Compute BEV grid parameters
        self.min_x = self.point_cloud_range[0]
        self.min_y = self.point_cloud_range[1]
        self.min_z = self.point_cloud_range[2]
        self.max_x = self.point_cloud_range[3]
        self.max_y = self.point_cloud_range[4]
        self.max_z = self.point_cloud_range[5]
        
        self.x_size = self.grid_size[0]
        self.y_size = self.grid_size[1]
        
        # Number of lane classes
        self.num_classes = len(self.lane_classes)
        
        # Number of z height bins
        self.num_z_bins = int((self.max_z - self.min_z) / self.voxel_size[2])
    
    def _generate_segmentation_mask(self, lanes, lane_labels, shape):
        """Generate lane segmentation mask in BEV.
        
        Args:
            lanes (list): List of lane point arrays.
            lane_labels (list): List of lane labels.
            shape (tuple): Shape of BEV grid (H, W).
            
        Returns:
            np.ndarray: Segmentation mask with shape (H, W).
        """
        # Initialize segmentation mask with ignore index
        segmentation = np.ones(shape, dtype=np.int64) * self.target_config['segmentation_ignore_index']
        
        # Draw lanes on segmentation mask
        line_width = self.target_config['line_width']
        
        for i, (lane, label) in enumerate(zip(lanes, lane_labels)):
            # Skip if label is invalid
            if label < 0 or label >= self.num_classes:
                continue
            
            # Convert 3D points to BEV grid coordinates
            points = []
            for point in lane:
                x, y = point[0], point[1]
                
                # Skip points outside the range
                if (x < self.min_x or x >= self.max_x or 
                    y < self.min_y or y >= self.max_y):
                    continue
                
                # Convert to grid coordinates
                grid_x = int((x - self.min_x) / (self.max_x - self.min_x) * self.x_size)
                grid_y = int((y - self.min_y) / (self.max_y - self.min_y) * self.y_size)
                
                # Ensure within grid bounds
                grid_x = min(max(0, grid_x), self.x_size - 1)
                grid_y = min(max(0, grid_y), self.y_size - 1)
                
                points.append((grid_x, grid_y))
            
            # Skip if no valid points
            if len(points) < 2:
                continue
            
            # Draw line with label as value
            for i in range(len(points) - 1):
                start = points[i]
                end = points[i + 1]
                
                # Use Bresenham algorithm for line drawing
                # This is a simple implementation - OpenCV's line drawing would be better
                x0, y0 = start
                x1, y1 = end
                
                dx = abs(x1 - x0)
                dy = abs(y1 - y0)
                sx = 1 if x0 < x1 else -1
                sy = 1 if y0 < y1 else -1
                err = dx - dy
                
                while x0 != x1 or y0 != y1:
                    # Draw thick line by setting nearby pixels
                    for dx in range(-line_width // 2, line_width // 2 + 1):
                        for dy in range(-line_width // 2, line_width // 2 + 1):
                            nx, ny = x0 + dx, y0 + dy
                            if 0 <= nx < self.x_size and 0 <= ny < self.y_size:
                                segmentation[ny, nx] = label + 1  # +1 as 0 is ignore index
                    
                    e2 = 2 * err
                    if e2 > -dy:
                        err -= dy
                        x0 += sx
                    if e2 < dx:
                        err += dx
                        y0 += sy
        
        return segmentation
    
    def _generate_instance_embedding(self, lanes, shape):
        """Generate instance embedding for lanes in BEV.
        
        Args:
            lanes (list): List of lane point arrays.
            shape (tuple): Shape of BEV grid (H, W).
            
        Returns:
            np.ndarray: Instance embedding with shape (H, W, embed_dim).
        """
        # Initialize embedding with zeros
        embed_dim = self.target_config['embed_dim']
        embedding = np.zeros((*shape, embed_dim), dtype=np.float32)
        
        # Draw lanes with unique embeddings
        line_width = self.target_config['line_width']
        
        for i, lane in enumerate(lanes):
            # Generate random embedding for this lane
            lane_embed = np.random.normal(0, 1, embed_dim).astype(np.float32)
            lane_embed = lane_embed / np.linalg.norm(lane_embed)
            
            # Convert 3D points to BEV grid coordinates
            points = []
            for point in lane:
                x, y = point[0], point[1]
                
                # Skip points outside the range
                if (x < self.min_x or x >= self.max_x or 
                    y < self.min_y or y >= self.max_y):
                    continue
                
                # Convert to grid coordinates
                grid_x = int((x - self.min_x) / (self.max_x - self.min_x) * self.x_size)
                grid_y = int((y - self.min_y) / (self.max_y - self.min_y) * self.y_size)
                
                # Ensure within grid bounds
                grid_x = min(max(0, grid_x), self.x_size - 1)
                grid_y = min(max(0, grid_y), self.y_size - 1)
                
                points.append((grid_x, grid_y))
            
            # Skip if no valid points
            if len(points) < 2:
                continue
            
            # Draw line with embedding
            for i in range(len(points) - 1):
                start = points[i]
                end = points[i + 1]
                
                # Use Bresenham algorithm for line drawing
                x0, y0 = start
                x1, y1 = end
                
                dx = abs(x1 - x0)
                dy = abs(y1 - y0)
                sx = 1 if x0 < x1 else -1
                sy = 1 if y0 < y1 else -1
                err = dx - dy
                
                while x0 != x1 or y0 != y1:
                    # Draw thick line with embedding
                    for dx in range(-line_width // 2, line_width // 2 + 1):
                        for dy in range(-line_width // 2, line_width // 2 + 1):
                            nx, ny = x0 + dx, y0 + dy
                            if 0 <= nx < self.x_size and 0 <= ny < self.y_size:
                                embedding[ny, nx] = lane_embed
                    
                    e2 = 2 * err
                    if e2 > -dy:
                        err -= dy
                        x0 += sx
                    if e2 < dx:
                        err += dx
                        y0 += sy
        
        return embedding
    
    def _generate_z_height_map(self, lanes, shape):
        """Generate z height map for lanes in BEV.
        
        Args:
            lanes (list): List of lane point arrays.
            shape (tuple): Shape of BEV grid (H, W).
            
        Returns:
            np.ndarray: Z height map with shape (H, W).
        """
        # Initialize height map with zeros
        height_map = np.zeros(shape, dtype=np.float32)
        # Initialize count map for averaging heights
        count_map = np.zeros(shape, dtype=np.float32)
        
        # Draw lanes with height information
        line_width = self.target_config['line_width']
        
        for lane in lanes:
            # Convert 3D points to BEV grid coordinates
            points = []
            for point in lane:
                x, y, z = point[0], point[1], point[2]
                
                # Skip points outside the range
                if (x < self.min_x or x >= self.max_x or 
                    y < self.min_y or y >= self.max_y or
                    z < self.min_z or z >= self.max_z):
                    continue
                
                # Normalize z height to [0, num_z_bins-1]
                z_norm = (z - self.min_z) / (self.max_z - self.min_z) * self.num_z_bins
                z_norm = min(max(0, z_norm), self.num_z_bins - 1)
                
                # Convert to grid coordinates
                grid_x = int((x - self.min_x) / (self.max_x - self.min_x) * self.x_size)
                grid_y = int((y - self.min_y) / (self.max_y - self.min_y) * self.y_size)
                
                # Ensure within grid bounds
                grid_x = min(max(0, grid_x), self.x_size - 1)
                grid_y = min(max(0, grid_y), self.y_size - 1)
                
                points.append((grid_x, grid_y, z_norm))
            
            # Skip if no valid points
            if len(points) < 2:
                continue
            
            # Draw line with height information
            for i in range(len(points) - 1):
                start = points[i]
                end = points[i + 1]
                
                # Use Bresenham algorithm for line drawing
                x0, y0, z0 = start
                x1, y1, z1 = end
                
                dx = abs(x1 - x0)
                dy = abs(y1 - y0)
                sx = 1 if x0 < x1 else -1
                sy = 1 if y0 < y1 else -1
                err = dx - dy
                
                # Interpolate z height
                if dx > dy:
                    z_step = (z1 - z0) / dx if dx > 0 else 0
                else:
                    z_step = (z1 - z0) / dy if dy > 0 else 0
                
                z = z0
                while x0 != x1 or y0 != y1:
                    # Draw thick line with height
                    for dx in range(-line_width // 2, line_width // 2 + 1):
                        for dy in range(-line_width // 2, line_width // 2 + 1):
                            nx, ny = x0 + dx, y0 + dy
                            if 0 <= nx < self.x_size and 0 <= ny < self.y_size:
                                height_map[ny, nx] += z
                                count_map[ny, nx] += 1
                    
                    e2 = 2 * err
                    if e2 > -dy:
                        err -= dy
                        x0 += sx
                        z += z_step * sx
                    if e2 < dx:
                        err += dx
                        y0 += sy
                        z += z_step * sy
        
        # Average heights where multiple values exist
        mask = count_map > 0
        height_map[mask] /= count_map[mask]
        
        # Convert to integer class indices for classification
        height_map = np.floor(height_map).astype(np.int64)
        height_map = np.clip(height_map, 0, self.num_z_bins - 1)
        
        return height_map
    
    def __call__(self, results):
        """Call function to generate lane targets for anchor-based detection.
        
        Args:
            results (dict): Result dict from loading pipeline.
            
        Returns:
            dict: Updated result dict with lane targets.
        """
        # Skip if no 3D lane annotations
        if 'gt_lanes_3d' not in results or len(results['gt_lanes_3d']) == 0:
            # Create empty targets
            target_shape = (self.y_size, self.x_size)
            
            segmentation = np.ones(target_shape, dtype=np.int64) * self.target_config['segmentation_ignore_index']
            embedding = np.zeros((*target_shape, self.target_config['embed_dim']), dtype=np.float32)
            height_map = np.zeros(target_shape, dtype=np.int64)
            
            results['lane_targets'] = {
                'segmentation': segmentation,
                'embedding': embedding,
                'height_map': height_map,
            }
            
            return results
        
        # Get lane annotations
        gt_lanes_3d = results['gt_lanes_3d']
        gt_lane_labels = results['gt_lane_labels']
        
        # Convert to numpy arrays if they are tensors
        lane_list = []
        label_list = []
        
        for i, lane in enumerate(gt_lanes_3d):
            if isinstance(lane, torch.Tensor):
                lane = lane.cpu().numpy()
            lane_list.append(lane)
            
            if i < len(gt_lane_labels):
                if isinstance(gt_lane_labels[i], torch.Tensor):
                    label = gt_lane_labels[i].cpu().numpy()
                else:
                    label = gt_lane_labels[i]
                label_list.append(label)
            else:
                label_list.append(0)  # Default label if missing
        
        # Generate targets
        target_shape = (self.y_size, self.x_size)
        
        # Generate segmentation mask
        if self.target_config['use_segmentation_mask']:
            segmentation = self._generate_segmentation_mask(lane_list, label_list, target_shape)
        else:
            segmentation = np.ones(target_shape, dtype=np.int64) * self.target_config['segmentation_ignore_index']
        
        # Generate instance embedding
        if self.target_config['use_instance_embedding']:
            embedding = self._generate_instance_embedding(lane_list, target_shape)
        else:
            embedding = np.zeros((*target_shape, self.target_config['embed_dim']), dtype=np.float32)
        
        # Generate z height map
        if self.target_config['use_z_height']:
            height_map = self._generate_z_height_map(lane_list, target_shape)
        else:
            height_map = np.zeros(target_shape, dtype=np.int64)
        
        # Save targets
        results['lane_targets'] = {
            'segmentation': segmentation,
            'embedding': embedding,
            'height_map': height_map,
        }
        
        return results
    
    def __repr__(self):
        repr_str = self.__class__.__name__
        repr_str += f'(point_cloud_range={self.point_cloud_range.tolist()}, '
        repr_str += f'voxel_size={self.voxel_size.tolist()}, '
        repr_str += f'grid_size={self.grid_size.tolist()}, '
        repr_str += f'lane_classes={self.lane_classes}, '
        repr_str += f'target_config={self.target_config})'
        return repr_str
# === Basic Settings 3D Lane Detection Based on Standard BEVFusion ===
dataset_type: Custom3DLaneDataset
dataset_root: /turbo/pcpt/data/lane_dataset/lane_3D_labeldata_0606_500frames
cam_list: ['60_front', '120_front', '120_left', '120_right', '120_back', 'right_back', 'left_back']
reduce_beams: 32
load_dim: 4
use_dim: 4  # Use all 4 dimensions: [x, y, z, intensity]
load_augmented: null

# Image size for all cameras (adapted from MogoB2 settings)
image_size: [256, 704]
max_epochs: 10

# FP16 training configuration
fp16:
  loss_scale:
    growth_interval: 2000

# === Core BEV & Voxel Settings ===
# CRITICAL: These ranges must be aligned between vtransform and point_cloud_range
voxel_size: [0.1, 0.1, 0.2]  # Standard voxel size

point_cloud_range: [0.0, -10.24, -1.0, 60.0, 10.24, 3.0]  # [x_min, y_min, z_min, x_max, y_max, z_max]
# Modified y range to use even values that produce more even grid dimensions for upsampling
camera_num: 7

# === Camera Augmentation Settings (per camera, adapted from MogoB2) ===
augment2d:
  # Resize limits per camera: [60_front, 120_front, 120_left, 120_right, 120_back, right_back, left_back]
  resize_train: [[0.184, 0.187], [0.38, 0.42], [0.38, 0.42], [0.38, 0.42], [0.38, 0.55], [0.38, 0.42], [0.38, 0.42]]
  resize_test: [[0.185, 0.185], [0.4, 0.4], [0.4, 0.4], [0.4, 0.4], [0.47, 0.47], [0.4, 0.4], [0.4, 0.4]]
  # Bottom crop limits per camera
  bot_pct_lim_train: [[0.0, 0.0], [0.12, 0.15], [0.2, 0.25], [0.2, 0.25], [0.15, 0.20], [0.2, 0.25], [0.2, 0.25]]
  bot_pct_lim_test: [[0.0, 0.0], [0.14, 0.14], [0.22, 0.22], [0.22, 0.22], [0.17, 0.17], [0.22, 0.22], [0.22, 0.22]]

# === Lane Task Specific ===
# Lane classes with IDs matching lane_line_config_v1.yaml (1-indexed)
lane_classes:
  - unknown # ID 0 (dummy class for background)
  - white-solid # ID 1
  - white-dashed # ID 2
  - white-double-solid # ID 3
  - white-solid-dashed # ID 4
  - white-dashed-solid # ID 5
  - white-double-dashed # ID 6
  - yellow-solid # ID 7
  - yellow-dashed # ID 8
  - yellow-double-solid # ID 9 
  - yellow-solid-dashed # ID 10
  - left-yellow-right-white-double-solid # ID 11
  - road-edge-dashed # ID 12

# === Input Modality ===
input_modality:
  use_lidar: true
  use_camera: true
  use_radar: false
  use_map: false
  use_external: false

# === Data Pipelines ===
train_pipeline:
  - type: LoadMultiViewImageFromFiles
    to_float32: true
  - type: LoadPointsFromFile
    coord_type: LIDAR
    load_dim: ${load_dim}
    use_dim: ${use_dim}
    reduce_beams: ${reduce_beams}
    with_lidarid: False
  - type: LoadLaneAnnotations3D
    with_lane_3d: true
    with_lane_label_3d: true
  
  # Optional: Load dense depth map for front camera (120_front)
  # Uncomment if depth maps are available
  # - type: LoadDenseDepthMapFromFile
  #   to_float32: true
  #   depth_scale: 256.0
  #   default_depth: 30.0
  #   max_depth: 80.0
  #   min_depth: 0.1
  
  # Image augmentation adapted from MogoB2Dataset
  - type: ImageAug3D
    final_dim: ${image_size}
    # resize_lim: [[1.0, 1.0], [1.0, 1.0], [1.0, 1.0], [1.0, 1.0], [1.0, 1.0], [1.0, 1.0], [1.0, 1.0]]  # No resize for debugging (7 cameras)
    # bot_pct_lim: [[0.0, 0.0], [0.0, 0.0], [0.0, 0.0], [0.0, 0.0], [0.0, 0.0], [0.0, 0.0], [0.0, 0.0]]  # No crop for debugging (7 cameras)
    # rot_lim: [0.0, 0.0]  # No rotation for debugging
    # rand_flip: false  # No flip for debugging
    camera_num: ${camera_num}
    resize_lim: ${augment2d.resize_train}  # Per-camera resize limits
    bot_pct_lim: ${augment2d.bot_pct_lim_train}  # Per-camera bottom crop limits
    rot_lim: [-5.4, 5.4]  # Rotation in degrees (adapted from MogoB2)
    rand_flip: true  # Enable random flip
    is_train: true
  
  - type: GlobalRotScaleTrans
    resize_lim: [1.0, 1.0]  # No resize for debugging
    rot_lim: [0.0, 0.0]  # No rotation for debugging
    trans_lim: 0.0  # No translation for debugging
    is_train: true
  
  - type: PointsRangeFilter
    point_cloud_range: ${point_cloud_range}
  
  - type: ImageNormalize
    mean: [0.485, 0.456, 0.406]
    std: [0.229, 0.224, 0.225]
  
  - type: GenerateBEVLaneHeatmapTargets
    point_cloud_range: ${point_cloud_range}
    voxel_size: ${voxel_size}
    # Updated grid_size to match final feature map dimensions from debug logs
    grid_size: [75, 26]  # Fixed to match feature map dimensions seen in debugging [75, 26]
    lane_classes: ${lane_classes}
      
    target_config:
      gaussian_sigma: 1.5
      heatmap_radius: 3
      max_lanes: 40
      num_points: 120
      generate_instance_ids: true
  
  - type: DefaultFormatBundle3D
    classes: ${lane_classes}
  
  - type: Collect3D
    keys:
      - img
      - points
      - gt_lanes_3d
      - gt_lane_labels
      - lane_targets
    meta_keys:
      - camera_intrinsics
      - camera2ego
      - lidar2ego
      - lidar2camera
      - camera2lidar
      - lidar2image
      - img_aug_matrix
      - lidar_aug_matrix

test_pipeline:
  - type: LoadMultiViewImageFromFiles
    to_float32: true

  - type: LoadPointsFromFile
    coord_type: LIDAR
    load_dim: ${load_dim}
    use_dim: ${use_dim}
    reduce_beams: ${reduce_beams}

  - type: LoadLaneAnnotations3D
    with_lane_3d: true
    with_lane_label_3d: true
  
  # Image augmentation for test (no augmentation)
  - type: ImageAug3D
    final_dim: ${image_size}
    camera_num: ${camera_num}
    resize_lim: ${augment2d.resize_test}  # Per-camera resize limits
    bot_pct_lim: ${augment2d.bot_pct_lim_test}  # Per-camera bottom crop limits
    rot_lim: [0.0, 0.0]  # No rotation for test
    rand_flip: false  # No flip for test
    is_train: false
  
  - type: ImageNormalize
    mean: [0.485, 0.456, 0.406]
    std: [0.229, 0.224, 0.225]
  - type: DefaultFormatBundle3D
    classes: ${lane_classes}
  - type: Collect3D
    keys:
      - img
      - points
      - gt_lanes_3d
      - gt_lane_labels
    meta_keys:
      - camera_intrinsics
      - camera2ego
      - lidar2ego
      - lidar2camera
      - camera2lidar
      - lidar2image
      - img_aug_matrix
      - lidar_aug_matrix

# --- Data Loading Config ---
data:
  samples_per_gpu: 1  # Single GPU debugging
  workers_per_gpu: 0  # No multiprocessing for debugging
  train:
    type: ${dataset_type}
    dataset_root: ${dataset_root}
    ann_file: ${dataset_root + "/train_annotations_skip20_without_depth_maps.pkl"}
    pipeline: ${train_pipeline}
    modality: ${input_modality}
    test_mode: false
    cam_list: ${cam_list}
    lane_classes: ${lane_classes}
    point_cloud_range: ${point_cloud_range}
  val:
    type: ${dataset_type}
    dataset_root: ${dataset_root}
    ann_file: ${dataset_root + "/val_annotations.pkl"}
    pipeline: ${test_pipeline}
    modality: ${input_modality}
    test_mode: false
    box_type_3d: LiDAR
    cam_list: ${cam_list}
    lane_classes: ${lane_classes}
    point_cloud_range: ${point_cloud_range}
  test:
    type: ${dataset_type}
    dataset_root: ${dataset_root}
    ann_file: ${dataset_root + "/test_info.pkl"}
    pipeline: ${test_pipeline}
    modality: ${input_modality}
    test_mode: true
    box_type_3d: LiDAR
    cam_list: ${cam_list}
    lane_classes: ${lane_classes}
    point_cloud_range: ${point_cloud_range}

# --- Evaluation ---
evaluation:
  interval: 1
  pipeline: ${test_pipeline}
  metric: 'OpenLane'
  eval_params:
     metric_list: ['f1_score', 'precision', 'recall', 'x_error_near', 'x_error_far', 'z_error']
     iou_threshold: 0.5

# --- Model Definition ---
model:
  type: BEVFusionForLanes
  encoders:
    camera:
      backbone:
        type: GPUNetWrapper
        model_type: GPUNet-1
        precision: fp16
        img_height: ${image_size[0]}
        img_width: ${image_size[1]}
        out_indices: [5, 10, 14]
        latency: 0.85ms
        gpuType: orin
        batch: 1
        pretrained: /configs/batch1/GV100/0.85ms.pth.tar
      # neck:
      #   type: GeneralizedLSSFPN
      #   in_channels: [192, 384, 768]
      #   out_channels: 256
      #   start_level: 0
      #   num_outs: 3
      #   norm_cfg:
      #     type: BN2d
      #     requires_grad: true
      #   act_cfg:
      #     type: ReLU
      #     inplace: true
      #   upsample_cfg:
      #     mode: bilinear
      #     align_corners: false
      neck:
        in_channels: [96, 288, 448]
      vtransform:
        # FIXED: Align with point_cloud_range
        xbound: [0.0, 60.0, 0.4]
        ybound: [-10.24, 10.24, 0.4]  # Adjusted to match point_cloud_range
        zbound: [-1.0, 3.0, 4.0]
        dbound: [1.0, 60.0, 0.5] #depth from 1m to 60m with 0.5m resolution
    lidar:
      voxelize:
        # max_num_points: 10
        point_cloud_range: ${point_cloud_range}
        voxel_size: ${voxel_size}
        max_voxels: [120000, 160000]  # Standard max voxels for 0.1m voxel size
      backbone:
        in_channels: ${use_dim}  # Match point cloud dimensions: [x, y, z, intensity]
        # Corrected sparse_shape calculations:
        # X: (60.0 - 0.0) / 0.1 = 600
        # Y: (10.24 - (-10.24)) / 0.1 = 204.8 = 205
        # Z: (3.0 - (-1.0)) / 0.2 = 20
        sparse_shape: [600, 205, 20]  # Fixed to match point_cloud_range and voxel_size
        encoder_paddings:
          - [0, 0, 1]
          - [0, 0, [1, 1, 0]]
          - [0, 0, 1]
          - [0, 0]
        encoder_strides:
          - [0, 0, 2]    # Layer 1: No XY stride, Z stride 2: [600, 205, 10]
          - [0, 0, 2]    # Layer 2: No XY stride, Z stride 2: [600, 205, 5] 
          - [0, 0, [2,2,1]]  # Layer 3: XY stride 2, Z stride 1: [300, 103, 5]
          - [0, 0]       # Layer 4: No stride: [300, 103, 5]

  # Add the fuser configuration with correct channel dimensions
  fuser:
    type: ConvFuser
    in_channels: [80, 128]  # Camera features (80) + LiDAR features (128)
    out_channels: 256

  decoder:
    backbone:
      type: SECOND
      in_channels: 256
      # out_channels: [128, 64, 128, 256]
      out_channels: [128, 128, 128, 128]
      # fiters_channels: [256,128, 128, 96]
      # layer_nums: [1, 2, 3, 1]
      layer_nums: [1, 2, 2, 1]
      # layer_strides: [1, 1, 2, 2]
      layer_strides: [1, 1, 2, 2]
    neck:
      type: SECONDFPN
      in_channels: [128, 128, 128, 128]
      out_channels: [128, 128, 128, 128]
      out_channel: 128
      upsample_strides: [1, 1, 2, 2]     # 对应backbone的strides
      use_conv_for_no_stride: true

  heads:
    lane:
      type: BEVLaneHeatmapHead
      in_channels: 256
      feat_channels: 64
      num_classes: ${len(lane_classes)}
      grid_conf:
        # FIXED: Align with point_cloud_range and match resolution with feature map
        xbound: [0.0, 60.0, 0.8]   # 60m/0.8m = 75 grid cells in x direction
        ybound: [-10.24, 10.24, 0.8] # 20m/0.8m = 25 grid cells in y direction (adjusted)
      row_points: 120
      z_range: [-1.0, 3.0]
      max_lanes: 40
      hm_thres: 0.25
      nms_kernel_size: 5
      use_sigmoid: true
      # Disable embedding for initial debugging
      use_embedding: false
      group_lanes: true
      lane_group_min_distance: 1.0
      loss_heatmap:
        type: GaussianFocalLoss
        alpha: 2.0
        gamma: 4.0
        reduction: mean
        loss_weight: 2.0
      loss_offset:
        type: L1Loss
        reduction: mean
        loss_weight: 1.5
      loss_z:
        type: L1Loss
        reduction: mean
        loss_weight: 1.5
      loss_cls:
        type: CrossEntropyLoss
        use_sigmoid: false
        reduction: mean
        loss_weight: 1.0

# --- Training Settings ---
optimizer:
  type: AdamW
  lr: 0.0001  # Lower learning rate for debugging
  weight_decay: 0.01
  betas: [0.9, 0.999]

lr_config:
  policy: CosineAnnealing
  warmup: linear
  warmup_iters: 100  # Shorter warmup for debugging
  warmup_ratio: 0.33333333
  min_lr_ratio: 1.0e-6

# # --- Essential Runtime Settings ---
# runner:
#   type: CustomEpochBasedRunner
#   max_epochs: ${max_epochs}

# checkpoint_config:
#   interval: 1
#   max_keep_ckpts: 5

# log_config:
#   interval: 1  # Log every iteration for debugging
#   hooks:
#     - type: TextLoggerHook
#     - type: TensorboardLoggerHook

# dist_params:
#   backend: nccl

# work_dir: ./work_dirs/lane_detection_standard_bevfusion_debug

# load_from: null
# resume_from: null

# cudnn_benchmark: false
# find_unused_parameters: true  # For debugging

# seed: 0
# deterministic: true  # For reproducible debugging
# Training Workflow

This document outlines the training workflow for the BEV-LaneDet model.

## Training Scripts

### OpenLane Dataset
- **[tools/train_openlane.py](mdc:tools/train_openlane.py)**: Main training script for OpenLane
- **[tools/val_openlane.py](mdc:tools/val_openlane.py)**: Evaluation script for OpenLane

### Apollo Dataset
- **[tools/train_apollo.py](mdc:tools/train_apollo.py)**: Main training script for Apollo
- **[tools/val_apollo.py](mdc:tools/val_apollo.py)**: Evaluation script for Apollo

## Configuration Files

- **[tools/openlane_config.py](mdc:tools/openlane_config.py)**: Configuration for OpenLane training
- **[tools/apollo_config.py](mdc:tools/apollo_config.py)**: Configuration for Apollo training

## Training Process

1. **Model Initialization**:
   - The model is initialized using the configuration file
   - Wrapped with `Combine_Model_and_Loss` class that handles loss computation

2. **Optimizer Setup**:
   - Uses AdamW optimizer
   - CosineAnnealingLR scheduler

3. **Training Loop**:
   - Iterates through epochs
   - For each batch:
     - Forward pass through model
     - Compute losses (segmentation, embedding, offset, z-coordinate)
     - Backward pass and optimization step
   - Periodically calculates F1 score for monitoring
   - Saves checkpoints at each epoch

4. **Loss Components**:
   - BEV segmentation loss (BCE + IoU)
   - BEV embedding loss (Push-Pull)
   - Offset prediction loss
   - Z-coordinate prediction loss
   - 2D segmentation loss
   - 2D embedding loss

## Model Checkpointing

- Models are saved at each epoch: `ep{epoch}.pth`
- Latest model is saved as `latest.pth`
- Checkpoints include model state and optimizer state
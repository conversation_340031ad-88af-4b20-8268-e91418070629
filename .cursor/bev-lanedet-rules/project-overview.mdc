# BEV-LaneDet Project Overview

This repository contains the implementation of BEV-LaneDet, an efficient and robust monocular 3D lane detection system. The project introduces:

1. **Virtual Camera**: Unifies intrinsic/extrinsic parameters of cameras mounted on different vehicles
2. **Key-Points Representation**: A simple but efficient 3D lane representation module
3. **Spatial Transformation Pyramid**: A lightweight module to transform front view features into BEV features

## Project Structure

- **[models/model/single_camera_bev.py](mdc:models/model/single_camera_bev.py)**: Core model architecture for BEV lane detection
- **[models/loss/__init__.py](mdc:models/loss/__init__.py)**: Loss functions for lane detection training
- **[loader/bev_road/openlane_data.py](mdc:loader/bev_road/openlane_data.py)**: OpenLane dataset loader
- **[loader/bev_road/apollo_data.py](mdc:loader/bev_road/apollo_data.py)**: Apollo dataset loader
- **[csrc/main.cpp](mdc:csrc/main.cpp)**: C++ implementation of Virtual Camera

## Training and Evaluation

- **[tools/train_openlane.py](mdc:tools/train_openlane.py)**: Training script for OpenLane dataset
- **[tools/val_openlane.py](mdc:tools/val_openlane.py)**: Evaluation script for OpenLane dataset
- **[tools/train_apollo.py](mdc:tools/train_apollo.py)**: Training script for Apollo dataset
- **[tools/val_apollo.py](mdc:tools/val_apollo.py)**: Evaluation script for Apollo dataset

## Configuration

- **[tools/openlane_config.py](mdc:tools/openlane_config.py)**: Configuration for OpenLane dataset
- **[tools/apollo_config.py](mdc:tools/apollo_config.py)**: Configuration for Apollo dataset
# Data Loading Pipeline

The BEV-LaneDet project uses custom data loaders for the OpenLane and Apollo 3D Lane Synthetic datasets.

## Dataset Classes

### OpenLane Dataset
- **[loader/bev_road/openlane_data.py](mdc:loader/bev_road/openlane_data.py)**:
  - `OpenLane_dataset_with_offset`: Training dataset with offset prediction
  - `OpenLane_dataset_with_offset_val`: Validation dataset with offset prediction

### Apollo Dataset
- **[loader/bev_road/apollo_data.py](mdc:loader/bev_road/apollo_data.py)**:
  - Similar dataset classes for Apollo 3D Lane Synthetic dataset

## Data Augmentation

Data augmentation is configured in the respective config files:
- **[tools/openlane_config.py](mdc:tools/openlane_config.py)**: OpenLane augmentation
- **[tools/apollo_config.py](mdc:tools/apollo_config.py)**: Apollo augmentation

The augmentation pipeline uses the Albumentations library and includes:
- Resizing
- Motion blur
- Brightness/contrast adjustments
- Color jitter
- Normalization

## Virtual Camera

The Virtual Camera is a key component that unifies camera parameters:
- Configured in the config files (`vc_config`)
- C++ implementation available in **[csrc/main.cpp](mdc:csrc/main.cpp)**

## BEV Transformation

The data loaders handle transformation from image space to Bird's Eye View (BEV):
- Defined range parameters (x_range, y_range)
- Grid size (meter_per_pixel)
- Output BEV shape calculation
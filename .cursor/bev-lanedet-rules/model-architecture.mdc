# BEV-LaneDet Model Architecture

The BEV-LaneDet model architecture is designed for efficient 3D lane detection using Bird's Eye View (BEV) representation.

## Core Components

### Main Model Class
- **[models/model/single_camera_bev.py](mdc:models/model/single_camera_bev.py)**: Contains the `BEV_LaneDet` class, which is the main model architecture.

### Key Components

1. **Instance Embedding Modules**:
   - `InstanceEmbedding`: Basic instance embedding module
   - `InstanceEmbedding_offset_y_z`: Enhanced instance embedding with offset and z-coordinate prediction

2. **Lane Head Modules**:
   - `LaneHeadResidual_Instance`: Lane detection head with instance embedding
   - `LaneHeadResidual_Instance_with_offset_z`: Enhanced lane detection head with offset and z-coordinate prediction

3. **Feature Transformation**:
   - `FCTransform_`: Fully connected transformation for feature maps
   - `Residual`: Residual block implementation

## Model Workflow

1. The model takes an image as input
2. Processes it through a backbone network (typically ResNet)
3. Transforms the features to BEV space
4. Applies lane detection heads to predict:
   - Lane segmentation
   - Lane instance embeddings
   - Offset predictions
   - Z-coordinate predictions

## Loss Functions

- **[models/loss/__init__.py](mdc:models/loss/__init__.py)**: Contains specialized loss functions:
  - `IoULoss`: For segmentation tasks
  - `NDPushPullLoss`: For instance embedding learning
{"mcpServers": {"filesystem": {"command": "npx", "args": ["-y", "@smithery/cli@latest", "run", "@smithery-ai/filesystem", "--key", "99b68a9d-3b9c-4d18-8f01-69aabdcf4a71", "--profile", "noisy-silverfish-v5Mwlb"]}, "github": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-github"], "env": {"GITHUB_PERSONAL_ACCESS_TOKEN": "*********************************************************************************************"}}, "desktop-commander": {"command": "npx", "args": ["-y", "@smithery/cli@latest", "run", "@wonderwhy-er/desktop-commander", "--key", "99b68a9d-3b9c-4d18-8f01-69aabdcf4a71"]}, "toolbox": {"command": "npx", "args": ["-y", "@smithery/cli@latest", "run", "@smithery/toolbox", "--key", "99b68a9d-3b9c-4d18-8f01-69aabdcf4a71", "--profile", "noisy-silverfish-v5Mwlb"]}, "context7-mcp": {"command": "npx", "args": ["-y", "@smithery/cli@latest", "run", "@upstash/context7-mcp", "--key", "99b68a9d-3b9c-4d18-8f01-69aabdcf4a71"]}, "clear-thought": {"command": "npx", "args": ["-y", "@smithery/cli@latest", "run", "@waldzellai/clear-thought", "--key", "99b68a9d-3b9c-4d18-8f01-69aabdcf4a71"]}, "git": {"command": "uvx", "args": ["mcp-server-git"]}}}
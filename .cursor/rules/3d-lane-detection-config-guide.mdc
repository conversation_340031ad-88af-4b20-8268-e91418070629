---
description: 
globs: 
alwaysApply: false
---
# 3D Lane Detection Configuration Guide

This rule provides comprehensive guidance for the 3D lane detection task implementation based on the BEVFusion framework, focusing on the configuration structure and debugging approach.

## Configuration Overview
# 3D Lane Detection Configuration Guide

This rule provides comprehensive guidance for the 3D lane detection task implementation based on the BEVFusion framework, focusing on the configuration structure and debugging approach.

## Configuration Overview

The main configuration file is [config_3d_lane_detection_task_based_bevfusion.yaml](mdc:configs/lane/config_3d_lane_detection_task_based_bevfusion.yaml), which implements a 3D lane detection task closely aligned with the native BEVFusion framework setup while maintaining consistency with other BEVFusion configurations like `v30_B2_second1231_3dseg_cls21_cam5lidar_intensityraw.yaml`.

## CRITICAL: BEV Coordinate System Alignment

**The most common cause of training errors is misalignment between `vtransform` bounds and `point_cloud_range`.**

### Proper Alignment Rules

1. **X-Y Range Consistency**: 
   ```yaml
   point_cloud_range: [0.0, -30, -1, 60, 30, 3.0]  # [x_min, y_min, z_min, x_max, y_max, z_max]
   vtransform:
     xbound: [0.0, 60.0, 0.1]    # Must match X range: [x_min, x_max, resolution]
     ybound: [-30.0, 30.0, 0.1]  # Must match Y range: [y_min, y_max, resolution]
     zbound: [-1.0, 3.0, 4.0]    # Must match Z range: [z_min, z_max, span]
   ```

2. **Resolution Calculation**: 
   - BEV grid size = (max - min) / resolution
   - For X: (60.0 - 0.0) / 0.1 = 600 bins
   - For Y: (30.0 - (-30.0)) / 0.1 = 600 bins
   - Total BEV grid: 600 × 600

3. **LiDAR Backbone Sparse Shape**:
   ```yaml
   sparse_shape: [600, 600, 20]  # [X_bins, Y_bins, Z_bins] calculated from ranges/voxel_size
   ```

### Common Misalignment Issues

- **Coordinate Mismatch**: vtransform bounds not matching point_cloud_range leads to geometric projection errors
- **Resolution Inconsistency**: Different resolutions between camera and LiDAR branches cause fusion problems
- **Empty BEV Pool**: No camera features land within the BEV grid, causing `IndexError: index -1 is out of bounds`

## Key Configuration Components

### Dataset & Input Configuration
- **Dataset Type**: `Custom3DLaneDataset` with multimodal input (LiDAR + Camera)
- **Camera Setup**: 7-camera configuration with specific naming convention:
  - `60_front`: Front-center camera (60° FOV)
  - `120_front`: Front wide-angle camera (120° FOV) 
  - `120_left`, `120_right`: Side cameras
  - `120_back`: Rear camera
  - `right_back`, `left_back`: Rear corner cameras
- **Point Cloud Range**: `[0.0, -30, -1, 60, 30, 3.0]` (60m forward, 30m sides, 4m height)
- **Voxel Size**: `[0.1, 0.1, 0.2]` (10cm x-y resolution, 20cm z resolution)

### Lane Classes
Supports 12 lane types including:
- Various white line types (solid, dashed, double)
- Yellow line types (solid, dashed, double)
- Mixed lane markings
- Road edge markings

### Model Architecture

#### Encoder Components
1. **Camera Encoder**:
   - Backbone: GPUNetWrapper (GPUNet-1) optimized for Orin platform
   - Neck: GeneralizedLSSFPN with 3 output levels
   - VTransform: DepthLSSTransform for BEV projection

2. **LiDAR Encoder**:
   - Voxelization with SparseEncoder backbone
   - 4-layer sparse convolution structure
   - Output channels: 128

#### Fusion & Decoding
- **Fuser**: ConvFuser combining camera (80 channels) and LiDAR (256 channels)
- **Decoder**: SECOND backbone + SECONDFPN neck for BEV feature processing

#### Detection Head (Primary Focus)
- **Type**: `BEVLaneHeatmapHead` - main component for debugging
- **Key Parameters**:
  - Grid configuration: 600x600 BEV grid
  - Row points: 120 for lane representation
  - Max lanes: 40 per frame
  - Heatmap threshold: 0.25
  - NMS kernel size: 5

## Current Debugging Setup

### Training Data
- **Scale**: 4 manually annotated frames for initial debugging
- **Purpose**: Validate BEVLaneHeatmapHead functionality and 3D lane prediction output

### Debugging Optimizations
- **Batch Size**: 1 (single GPU debugging)
- **Workers**: 0 (no multiprocessing)
- **Learning Rate**: 0.0001 (reduced for stability)
- **Max Epochs**: 10 (limited for debugging cycles)
- **Logging**: Every iteration for detailed monitoring

### Disabled Features (Temporarily)
- **Depth Maps**: LoadDenseDepthMapFromFile pipeline step commented out
- **Lane Embedding**: `use_embedding: false` in BEVLaneHeatmapHead
- **Heavy Augmentations**: Reduced for debugging stability

## File Structure References

### Core Implementation Files
- **Model Definition**: [mmdet3d/models/fusion_models/](mdc:mmdet3d/models/fusion_models) - BEVFusionForLanes
- **Lane Head**: [mmdet3d/models/heads/lane/](mdc:mmdet3d/models/heads/lane) - BEVLaneHeatmapHead implementation
- **Dataset**: [mmdet3d/datasets/](mdc:mmdet3d/datasets) - Custom3DLaneDataset
- **Pipelines**: [mmdet3d/datasets/pipelines/](mdc:mmdet3d/datasets/pipelines) - Data loading and augmentation

### Configuration Hierarchy
- **Base Config**: [configs/lane/](mdc:configs/lane) - Lane detection configurations
- **Model Configs**: [mmdet3d/models/backbones/configs/](mdc:mmdet3d/models/backbones/configs) - GPUNet configurations

## Data Pipeline Flow

### Training Pipeline
1. **LoadMultiViewImageFromFiles** - Load 7-camera images
2. **LoadPointsFromFile** - Load LiDAR point clouds
3. **LoadLaneAnnotations3D** - Load 3D lane annotations
4. **ImageAug3D** - Per-camera augmentation with MogoB2 settings
5. **GlobalRotScaleTrans** - Global 3D augmentation
6. **PointsRangeFilter** - Filter points by range
7. **ImageNormalize** - Standard ImageNet normalization
8. **GenerateBEVLaneHeatmapTargets** - Generate BEV heatmap targets
9. **DefaultFormatBundle3D** + **Collect3D** - Final formatting

### Key Pipeline Parameters
- **Gaussian Sigma**: 1.5 for heatmap generation
- **Heatmap Radius**: 3 pixels
- **Instance IDs**: Generated for lane tracking

## Loss Configuration

Multiple loss components for comprehensive training:
- **Heatmap Loss**: GaussianFocalLoss (weight: 2.0)
- **Offset Loss**: L1Loss (weight: 1.5)
- **Z-coordinate Loss**: L1Loss (weight: 1.5)
- **Classification Loss**: CrossEntropyLoss (weight: 1.0)

## Debugging Focus Areas

1. **BEVLaneHeatmapHead Output**: Primary debugging target for 3D lane predictions
2. **Multi-camera Integration**: Ensure all 7 cameras contribute properly
3. **BEV Projection**: Verify camera-to-BEV transformation accuracy
4. **Heatmap Generation**: Validate target generation and prediction alignment
5. **Lane Grouping**: Test lane association and grouping algorithms

## Usage Notes

- Configuration follows BEVFusion v30_B2 structure for compatibility
- Optimized for Orin platform deployment
- FP16 training enabled for memory efficiency
- Deterministic settings for reproducible debugging
- Single GPU setup for initial validation

This configuration serves as the foundation for 3D lane detection development within the BEVFusion framework, with emphasis on debugging the core detection head functionality.

## Debugging Strategy & Error Resolution

### Primary Error: `IndexError: index -1 is out of bounds for dimension 0 with size 0`

**Root Cause**: BEV pooling operation receives empty geometry features due to coordinate misalignment.

**Most Common Cause**: Calibration data not loaded properly due to relative path issue.

**CRITICAL FIXES IMPLEMENTED**: 

1. **Fixed calibration path loading** in [mmdet3d/datasets/custom_3d_lane_dataset.py](mdc:mmdet3d/datasets/custom_3d_lane_dataset.py):
```python
# BEFORE (broken): calib_path = "train/segment_01/calibration.json"
# AFTER (fixed): full_calib_path = osp.join(self.dataset_root, calib_path)
```

2. **Fixed numpy array formatting error** in [mmdet3d/models/vtransforms/base.py](mdc:mmdet3d/models/vtransforms/base.py):
```python
# BEFORE (broken): f"{self.dx.cpu().numpy():.3f}"  # TypeError on numpy array
# AFTER (fixed): f"[{dx_values[0]:.3f}, {dx_values[1]:.3f}, {dx_values[2]:.3f}]"  # Format each element
```

3. **Fixed LiDAR backbone channel dimension mismatch**:
```yaml
# BEFORE (broken): in_channels: 5  # Expected 5D but got 4D points
# AFTER (fixed): in_channels: 4   # Match actual point dimensions [x,y,z,intensity]
```

**Debugging Process**:
1. **Check Calibration Loading**:
   ```
   [CALIB_DEBUG] Frame X: calib_path (relative) = train/segment_01/calibration.json
   [CALIB_DEBUG] Full calibration path: /dataset_root/train/segment_01/calibration.json
   [CALIB_DEBUG] Successfully loaded calibration data with keys: ['60_front', '120_front', ...]
   ```

2. **Verify Camera Calibration**:
   ```
   [CALIB_DEBUG] Camera 60_front: found calibration data
   [CALIB_DEBUG]   Intrinsic focal lengths: fx=3769.0, fy=3769.0
   [CALIB_DEBUG]   Extrinsic translation: [4.600, 0.020, 2.800]
   ```

3. **Check BEV Pool Debug Output**:
   ```
   [BEV_POOL_DEBUG] Points kept after filtering: X/Y (Z%)  # Should be > 0%
   ```

4. **Coordinate Range Analysis**:
   - Check if projected camera features fall within BEV bounds
   - Verify camera-to-LiDAR transformations are reasonable

### Configuration Validation Checklist

- [ ] **Coordinate Alignment**: `vtransform.xbound/ybound` matches `point_cloud_range`
- [ ] **Resolution Consistency**: BEV grid sizes align across components
- [ ] **Calibration Data**: All cameras have valid intrinsic/extrinsic parameters
- [ ] **Data Paths**: LiDAR and image paths exist and are accessible
- [ ] **Lane Annotations**: Ground truth lanes are within the specified range

### Essential Debug Messages to Monitor

Keep these debug messages for effective troubleshooting:
- `[BEV_POOL_DEBUG]`: BEV pooling coordinate analysis
- `[CALIB_DEBUG/WARNING/ERROR]`: Calibration loading status
- `[DATA_ERROR/WARNING]`: Data loading issues
- `[PIPELINE_ERROR]`: Pipeline processing errors

Remove verbose sample-level logging to reduce noise.

## Performance Optimization Notes

### Memory & Computation
- **Image Size**: `[256, 704]` with feature size `[32, 88]` (8x downsampling)
- **Depth Sampling**: 118 depth bins from 1m to 60m (0.5m resolution)
- **BEV Resolution**: 0.1m provides good accuracy vs. memory trade-off

### Camera-Specific Settings
- **Augmentation**: Per-camera resize and crop limits adapted from MogoB2
- **Depth Maps**: Optional dense depth loading for front camera (`120_front`)
- **Multi-Camera Fusion**: All 7 cameras contribute to BEV representation

## Architecture Principles

### BEVFusion Framework Integration
- **Minimal Modification**: Reuses existing BEVFusion components where possible
- **Modular Design**: Lane detection head can be swapped/modified independently
- **Multi-Task Ready**: Framework supports adding other perception tasks

### Lane Detection Specifics
- **Heatmap-Based**: Uses Gaussian heatmaps for lane point representation
- **3D-Aware**: Outputs full 3D lane coordinates (X, Y, Z)
- **Multi-Class**: Supports 12 different lane types (solid, dashed, colors, etc.)

## Troubleshooting Reference

### Common Training Issues
1. **Empty BEV Pool**: Fix coordinate alignment
2. **CUDA OOM**: Reduce batch size or image resolution
3. **NaN Losses**: Check learning rate and loss weights
4. **No Lane Detections**: Verify ground truth annotation format

### Configuration Tuning
- **For Higher Accuracy**: Increase BEV resolution (smaller voxel_size)
- **For Speed**: Reduce image resolution or depth sampling
- **For Memory**: Decrease max_voxels or use FP16 more aggressively
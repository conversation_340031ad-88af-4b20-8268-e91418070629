---
description: 
globs: 
alwaysApply: true
---
# Project Rules for 3D Lane Detection Implementation

- Analyze the multi-modal multi-task BEVFusion framework thoroughly to understand each module's functionality
- Maximize reuse of existing modules when implementing lane detection capabilities
- Reference provided configuration files to identify and understand relevant code components
- Follow existing code style and patterns for new implementations
- Ensure new code integrates seamlessly with the existing architecture
- Verify all imports and variable references to prevent undefined references
- Implement comprehensive error handling with descriptive messages
- Design modular components with clear interfaces for extensibility
- Create appropriate configuration files following existing structure
- Document all new modules with clear descriptions
- Test integration with the overall perception pipeline
---
description: 
globs: 
alwaysApply: false
---
# 3D Lane Detection in BEVFusion Framework

## Overview
The 3D Lane Detection task is integrated into the existing BEVFusion multi-modal multi-task perception framework, which originally supports 3D object detection and 3D semantic segmentation. This rule provides guidance on understanding the codebase structure related to 3D lane detection task implementation.

## Architecture Overview

### BEVFusionForLanes Model
The [mmdet3d/models/fusion_models/bevfusion_lanes.py](mdc:mmdet3d/models/fusion_models/bevfusion_lanes.py) is the central model for 3D lane detection that extends the core BEVFusion architecture:

- **Encoder Stage**: Processes camera and LiDAR inputs separately
  - Camera: Extracts features using a CNN backbone and transforms to BEV space
  - LiDAR: Voxelizes points and processes them through a sparse 3D backbone
  
- **Fusion Stage**: Combines features from different modalities
  - Uses ConvFuser to fuse camera and LiDAR features in BEV space
  
- **Decoder Stage**: Further processes the fused features
  - Uses SECOND backbone and FPN for feature refinement
  
- **Lane Detection Head**: Processes features to detect lanes
  - Supports heatmap-based lane detection with embedding for point grouping

### Depth-Based View Transformation
The [mmdet3d/models/vtransforms/depth_lss.py](mdc:mmdet3d/models/vtransforms/depth_lss.py) contains specialized view transformation modules:

- **DepthLSSTransformWithDepthMap**: Extends the standard LSS transform to support dense depth maps
  - Uses pre-computed depth maps from the primary camera (front 120 camera)
  - Enhances BEV feature quality by providing more accurate depth information
  - Creates depth distribution to properly weight features in 3D space

## Key Components

### Dataset Structure
- [mmdet3d/datasets/custom_3d_lane_dataset.py](mdc:mmdet3d/datasets/custom_3d_lane_dataset.py): Main dataset class for 3D lane detection that inherits from Custom3DDataset
- [mmdet3d/datasets/__init__.py](mdc:mmdet3d/datasets/__init__.py): Registers the lane dataset class
- [mmdet3d/datasets/builder.py](mdc:mmdet3d/datasets/builder.py): Builds the dataset instance during training/testing

### Pipeline Components
- [mmdet3d/datasets/pipelines/transforms_3d.py](mdc:mmdet3d/datasets/pipelines/transforms_3d.py): Contains the LaneRangeFilter for filtering lane lines outside the point cloud range
- [mmdet3d/datasets/pipelines/lane_processing.py](mdc:mmdet3d/datasets/pipelines/lane_processing.py): Specialized transformations for lane data
- [mmdet3d/datasets/pipelines/loading.py](mdc:mmdet3d/datasets/pipelines/loading.py): Contains LoadLaneAnnotations3D for loading lane annotations and 
- [mmdet3d/datasets/pipelines/__init__.py](mdc:mmdet3d/datasets/pipelines/__init__.py): Registers all pipeline components

### Model Heads
- [mmdet3d/models/heads/lane/bev_lane_heatmap_head.py](mdc:mmdet3d/models/heads/lane/bev_lane_heatmap_head.py): BEVLaneHeatmapHead for heatmap-based lane detection
- [mmdet3d/models/heads/lane/anchor_lane_head.py](mdc:mmdet3d/models/heads/lane/anchor_lane_head.py): BEVLaneAnchorHead for anchor-based lane detection
- [mmdet3d/models/heads/lane/__init__.py](mdc:mmdet3d/models/heads/lane/__init__.py): Imports and registers lane detection heads

### Configuration Files
- [configs/lane/config_dense_depth_embedding_lane_detection.yaml](mdc:configs/lane/config_dense_depth_embedding_lane_detection.yaml): Main config for dense depth embedding lane detection
- [configs/lane/config_dense_depth_heatmap_lane_detection.yaml](mdc:configs/lane/config_dense_depth_heatmap_lane_detection.yaml): Config for heatmap-based lane detection
- [configs/lane/config_dense_depth_anchor_lane_detection.yaml](mdc:configs/lane/config_dense_depth_anchor_lane_detection.yaml): Config for anchor-based lane detection

### Evaluation Tools
- [tools/eval_3d_lane_openlane/eval_3D_lane.py](mdc:tools/eval_3d_lane_openlane/eval_3D_lane.py): Lane evaluation using OpenLane metrics
- [tools/utils/validate_lane_detection.py](mdc:tools/utils/validate_lane_detection.py): Validation script for lane detection
- [tools/utils/test_lane_grouping.py](mdc:tools/utils/test_lane_grouping.py): Testing script for lane grouping functionality

### Data Processing Tools
- [tools/process_lane_dataset_pkl.py](mdc:tools/process_lane_dataset_pkl.py): Processes raw 3D lane dataset and generates pickle files
- [tools/visualize_lane_dataset.py](mdc:tools/visualize_lane_dataset.py): Visualizes the 3D lane detection dataset

### Inference
- [tools/inference_3d_lane_detection.py](mdc:tools/inference_3d_lane_detection.py): Inference script for 3D lane detection

## Dataset Format
The lane detection dataset follows this structure:
```
dataset_root/
├── train/
│   ├── segment_001/
│   │   ├── images/
│   │   │   ├── 120_front/
|   |   |   |      |-----1.jpg (1920 x 1080)
│   │   │   ├── 120_left/
|   |   |   |      |-----1.jpg (1920 x 1080)
│   │   │   ├── 120_right/
|   |   |   |      |-----1.jpg (1920 x 1080)
│   │   │   ├── 120_back/
|   |   |   |      |-----1.jpg (1920 x 1080)
│   │   │   ├── right_back/
|   |   |   |      |-----1.jpg (1920 x 1080)
│   │   │   └── left_back/
|   |   |   |      |-----1.jpg (1920 x 1080)
│   │   ├── lidar/
|   |   |     |------1.pcd
│   │   ├── depth_maps/
|   |   |     |------1.png
│   │   ├── annotations/
|   |   |     |------1.json
│   │   ├── pose.txt
│   │   └── calibration.json
│   └── ...
├── val/
└── test/
```
# introduce depth_maps
# PENet Depth Output Format and Representation Analysis

## 1. Output Format: PNG Image Files

**Yes, PENet outputs depth results as PNG image files.** Specifically:

- **File format**: 16-bit PNG files (`.png` extension)
- **Save function**: `save_depth_as_uint16png_upload()` in `vis_utils.py` (lines 99-105)
- **Alternative formats**: The codebase also supports colored visualization as 8-bit PNG files using `save_depth_as_uint8colored()`

## 2. Depth Information Representation

### 2.1 Data Structure and Type

**PyTorch Tensor → NumPy Array → PNG Image**

```python
# Original model output (PyTorch tensor)
pred: torch.Tensor  # Shape: [B, 1, H, W], dtype: torch.float32

# Conversion to NumPy for saving
img = np.squeeze(img.data.cpu().numpy())  # Remove batch dimension
img = (img * 256.0).astype('uint16')      # Scale and convert to uint16
```

### 2.2 Encoding Principle

**Core Encoding Formula:**
```
PNG_value = depth_in_meters × 256
```

**Key characteristics:**
- **Input depth range**: Real-world depth values in meters (typically 0-80m for KITTI)
- **Scaling factor**: Multiply by 256 for storage precision
- **Data type**: `uint16` (16-bit unsigned integer)
- **Value range**: 0-65535 in PNG file
- **Resolution**: 1/256 ≈ 0.0039 meters precision

### 2.3 Decoding Process

**To recover actual depth values:**
```python
# Load PNG file
depth_png = np.array(Image.open(filename), dtype=int)

# Convert back to meters
actual_depth = depth_png.astype(np.float) / 256.0
```

## 3. Tensor Output Structure

### 3.1 Model Output Dimensions

```python
# PENet model forward output
output: torch.Tensor  # Shape: [batch_size, 1, height, width]

# Standard KITTI dimensions
height = 352    # Training: 320 (random crop), Validation: 352
width = 1216    # Training: varies, Validation: 1216
```

### 3.2 Processing Pipeline

```python
# 1. Model prediction (float32 tensor)
pred = model(input_batch)  # [1, 1, 352, 1216]

# 2. Resize to target dimensions if needed
pred_resized = torch.nn.functional.interpolate(
    pred, size=(1080, 1920), mode='nearest'
)

# 3. Convert to numpy and scale
pred_np = pred_resized.squeeze().detach().cpu().numpy()
scaled_depth = (pred_np * 256.0).astype('uint16')

# 4. Save as 16-bit PNG
Image.fromarray(scaled_depth, mode='I').save(filename)
```

## 4. BEVFusion Integration Considerations

### 4.1 Format Compatibility

**For BEVFusion LSS replacement:**

1. **Depth Range**: PENet outputs continuous depth values, while LSS typically uses discrete probability distributions
2. **Coordinate System**: Both use camera coordinate systems but may need calibration alignment
3. **Resolution**: PENet native resolution (352×1216) vs. your target resolution
4. **Data Type**: Convert from uint16 PNG back to float32 tensors

### 4.2 Integration Code Template

```python
def load_penet_depth(png_path):
    """Load PENet depth result for BEVFusion integration"""
    # Load 16-bit PNG
    depth_png = np.array(Image.open(png_path), dtype=np.uint16)
    
    # Convert to actual depth values (meters)
    depth_meters = depth_png.astype(np.float32) / 256.0
    
    # Convert to torch tensor
    depth_tensor = torch.from_numpy(depth_meters).unsqueeze(0)  # Add channel dim
    
    return depth_tensor  # Shape: [1, H, W]

def convert_depth_to_lss_format(depth_tensor, depth_bins):
    """Convert continuous depth to LSS probability format"""
    # Create probability distribution around predicted depth
    # This is a simplified example - you may need more sophisticated conversion
    probs = torch.zeros(len(depth_bins), *depth_tensor.shape[1:])
    
    # Find closest depth bin for each pixel
    depth_indices = torch.bucketize(depth_tensor, depth_bins)
    
    # Set probability to 1.0 at predicted depth bin
    probs.scatter_(0, depth_indices, 1.0)
    
    return probs
```

## 5. Summary

- **Format**: 16-bit PNG files with filename extensions `.png`
- **Encoding**: `PNG_value = depth_meters × 256`
- **Precision**: ~0.0039 meters (1/256)
- **Range**: 0-256 meters maximum (0-65535 PNG values)
- **Structure**: Single-channel grayscale images
- **Integration**: Requires decoding PNG → float32 → probability distribution for BEVFusion LSS

This depth representation provides high precision suitable for autonomous driving applications while maintaining efficient storage in standard image formats.


## Key Concepts

### Lane Representation
Lanes are represented as sets of 3D points (x, y, z) in the LiDAR coordinate system, where:
- x: lateral position (right is positive)
- y: longitudinal position (forward is positive)
- z: height (up is positive)

### Lane Detection Approaches
1. **Heatmap-based approach**: Detects lane keypoints on a BEV heatmap and groups them into lanes
3. **Embedding-based approach**: Uses embedding vectors to group lane points belonging to the same lane

### Lane Grouping Methods
The `BEVLaneHeatmapHead` supports different lane point grouping strategies:
- Geometric distance-based grouping
- Embedding-based clustering using DBSCAN

### Integration with BEVFusion
The lane detection task leverages the BEVFusion backbone which:
1. Extracts features from camera and LiDAR inputs
2. Projects them to BEV space
3. Fuses multi-modal features
4. Passes the fused features to task-specific heads

## Common Patterns
1. The pipeline transforms follow mmdet3d patterns but with lane-specific components
2. Camera images and depth maps are used alongside LiDAR data
3. The training follows the single-task paradigm but uses the multi-task backbone
4. OpenLane evaluation metrics (F1 score, precision, recall, x/z errors) are used for benchmarking

## Implementation Details

### BEVFusionForLanes
The `BEVFusionForLanes` model in [bevfusion_lanes.py](mdc:mmdet3d/models/fusion_models/bevfusion_lanes.py) has several specialized features:

1. **Depth-aware Feature Extraction**:
   - Supports direct depth map integration in `extract_camera_features()`
   - Passes depth maps to view transformation for improved accuracy
   - Provides depth supervision during training

2. **Flexible Lane Head Integration**:
   - Supports different lane detection heads through a unified interface
   - Handles both heatmap-based and anchor-based lane detection
   - Properly processes both single-stage and multi-stage lane detection outputs

3. **Multi-stage Processing in forward_single()**:
   - Encoder stage processes each sensor modality
   - Fusion stage combines multi-modal features
   - Decoder stage refines the features
   - Branching for either training (loss computation) or inference (lane predictions)

### DepthLSSTransformWithDepthMap
The specialized view transformation in [depth_lss.py](mdc:mmdet3d/models/vtransforms/depth_lss.py) enhances BEV feature quality:

1. **Camera-specific Processing**:
   - Uses dense depth maps for the primary camera (front view)
   - Falls back to standard LSS depth estimation for other cameras
   - Maintains backward compatibility with regular LSS transform

2. **Depth Distribution Generation**:
   - Creates depth distribution to properly weight features in 3D space
   - For primary camera: Uses pre-computed depth map directly
   - For other cameras: Uses network-estimated depth

3. **Integration with BEV Pooling**:
   - Projects features to 3D space based on camera parameters
   - Aggregates features into BEV grid using efficient pooling
   - Handles camera augmentations correctly

## Configuration Analysis
The [config_dense_depth_embedding_lane_detection.yaml](mdc:configs/lane/config_dense_depth_embedding_lane_detection.yaml) configures:

1. **Dataset Settings**:
   - Uses Custom3DLaneDataset with 6 cameras
   - Configures camera augmentation parameters per camera
   - Defines 12 lane classes for various line types

2. **Pipeline Configuration**:
   - Loads multi-view images, point clouds, lane annotations, and depth maps
   - Performs data augmentation with ImageAug3D and GlobalRotScaleTrans
   - Generates BEV lane heatmap targets for training

3. **Model Architecture**:
   - Uses GPUNet backbone for camera features
   - Incorporates DepthLSSTransformWithDepthMap for view transformation
   - Uses SparseEncoder for LiDAR processing
   - Configures BEVLaneHeatmapHead with embedding-based lane grouping

4. **Training Strategy**:
   - Loads weights from a pre-trained multi-task BEVFusion model
   - Uses AdamW optimizer with cosine annealing scheduler
   - Evaluates using OpenLane metrics

## Best Practices
<!-- 1. Use `LaneRangeFilter` to filter out lanes outside the point cloud range -->
2. Match camera_num parameter with the number of cameras in cam_list
3. Configure proper bot_pct_lim values for each camera in the ImageAug3D transform
4. Utilize embedding-based lane grouping for complex scenarios with nearby lanes
5. Properly set the primary_cam_index to match the front camera position in the cam_list
6. When using dense depth maps, ensure depth_map and depth_map_valid are passed to the model
7. For complex multi-lane scenarios, use the embedding-based lane grouping with appropriate clustering parameters
#!/usr/bin/env python
"""
Visualization utilities for 3D lane detection results.

This module provides functions for visualizing 3D lane detection results in:
1. <PERSON>'s Eye View (BEV)
2. Camera view (projection onto images)
3. 3D visualization
"""

import os
import os.path as osp
import json
import numpy as np
import cv2
import matplotlib.pyplot as plt
from matplotlib import cm
from mpl_toolkits.mplot3d import Axes3D
import warnings

# Suppress matplotlib warnings that might occur during visualization
warnings.filterwarnings("ignore", category=UserWarning, module="matplotlib")


def visualize_bev_lanes(
    lanes, 
    lane_types, 
    lane_scores, 
    output_path=None, 
    point_cloud_range=None, 
    show_score_thr=0.3,
    lane_colors=None,
    lane_classes=None,
    title=None,
    figsize=(10, 8)
):
    """Visualize lanes in Bird's Eye View (BEV).
    
    Args:
        lanes (list): List of lane points in 3D.
        lane_types (list): List of lane types.
        lane_scores (list): List of lane confidence scores.
        output_path (str, optional): Path to save visualization. If None, figure is returned.
        point_cloud_range (list, optional): Range of point cloud in format [x_min, y_min, z_min, x_max, y_max, z_max].
            Defaults to [0.0, -48.0, -1.0, 97.6, 48.0, 3.0].
        show_score_thr (float, optional): Threshold for showing lanes. Defaults to 0.3.
        lane_colors (dict, optional): Custom colors for lane types. Defaults to None.
        lane_classes (list, optional): Names of lane classes. Defaults to None.
        title (str, optional): Custom title for the plot. Defaults to "Bird's Eye View Lane Detection".
        figsize (tuple, optional): Figure size (width, height). Defaults to (10, 8).
        
    Returns:
        np.ndarray or None: Visualization image if output_path is None, otherwise None.
    """
    if point_cloud_range is None:
        point_cloud_range = [0.0, -48.0, -1.0, 97.6, 48.0, 3.0]
    
    # Extract point cloud range for visualization
    x_min, y_min, _, x_max, y_max, _ = point_cloud_range
    
    # Create figure and axis
    plt.figure(figsize=figsize)
    ax = plt.gca()
    
    # Set axis limits based on point cloud range
    ax.set_xlim(x_min, x_max)
    ax.set_ylim(y_min, y_max)
    
    # Draw grid
    ax.grid(True)
    
    # Set labels and title
    ax.set_xlabel('X (m)')
    ax.set_ylabel('Y (m)')
    plot_title = title if title else "Bird's Eye View Lane Detection"
    ax.set_title(plot_title)
    
    # Get default color map for lane types if not provided
    if lane_colors is None:
        cmap = cm.get_cmap('tab10')
    
    # Plot each lane
    lanes_by_type = {}
    
    for lane, lane_type, score in zip(lanes, lane_types, lane_scores):
        if score < show_score_thr:
            continue
            
        # Convert to numpy array if needed
        lane = np.array(lane)
        if len(lane) == 0 or lane.shape[0] < 2:  # Need at least two points
            continue
            
        # Extract x, y coordinates
        x, y = lane[:, 0], lane[:, 1]
        
        # Get lane type name if lane_classes is provided
        if lane_classes and lane_type < len(lane_classes):
            type_name = lane_classes[lane_type]
        else:
            type_name = f"Type {lane_type}"
        
        # Get lane color
        if lane_colors is not None and lane_type in lane_colors:
            color = lane_colors[lane_type]
        else:
            color = cmap(lane_type % 10)
        
        # Plot lane with color based on lane type
        label = f"{type_name}, score: {score:.2f}"
        
        # Group lanes by type for legend
        if type_name not in lanes_by_type:
            lanes_by_type[type_name] = {'color': color, 'score': score}
            ax.plot(x, y, '-', color=color, linewidth=2, label=label)
        else:
            ax.plot(x, y, '-', color=color, linewidth=2)
        
        # Add arrow to show direction
        if len(x) > 2:
            idx = len(x) // 2
            dx, dy = x[idx+1] - x[idx-1], y[idx+1] - y[idx-1]
            norm = np.sqrt(dx*dx + dy*dy)
            if norm > 0:
                dx, dy = dx/norm, dy/norm
                ax.arrow(x[idx], y[idx], dx, dy, head_width=0.5, 
                        head_length=0.7, fc=color, ec=color)
    
    # Add ego vehicle marker
    ax.plot(0, 0, 'ko', markersize=10, label='Ego Vehicle')
    
    # Add rectangular outline for ego vehicle
    from matplotlib.patches import Rectangle
    ego_rect = Rectangle((-1, -1), 2, 4, linewidth=1, edgecolor='black', facecolor='none')
    ax.add_patch(ego_rect)
    
    # Add legend
    ax.legend(loc='upper right')
    
    # Save figure or return the image
    plt.tight_layout()
    
    if output_path:
        os.makedirs(osp.dirname(output_path), exist_ok=True)
        plt.savefig(output_path, dpi=200, bbox_inches='tight')
        plt.close()
        return None
    else:
        # Convert matplotlib figure to numpy array for return
        fig = plt.gcf()
        fig.canvas.draw()
        img = np.array(fig.canvas.renderer.buffer_rgba())
        plt.close()
        return img


def visualize_camera_lanes(
    lanes, 
    lane_types, 
    lane_scores, 
    camera_intrinsics, 
    lidar2camera, 
    cam_img=None, 
    output_path=None, 
    show_score_thr=0.3,
    lane_colors=None,
    lane_classes=None,
    thickness=2,
    font_scale=0.5
):
    """Visualize lanes in camera view.
    
    Args:
        lanes (list): List of lane points in 3D.
        lane_types (list): List of lane types.
        lane_scores (list): List of lane confidence scores.
        camera_intrinsics (np.ndarray): Camera intrinsic matrix (3x3 or 4x4).
        lidar2camera (np.ndarray): Transformation from LiDAR to camera coordinate (4x4).
        cam_img (np.ndarray, optional): Camera image. If None, a blank image is created.
        output_path (str, optional): Path to save visualization. If None, image is returned.
        show_score_thr (float, optional): Threshold for showing lanes. Defaults to 0.3.
        lane_colors (dict, optional): Custom colors for lane types. Defaults to None.
        lane_classes (list, optional): Names of lane classes. Defaults to None.
        thickness (int, optional): Line thickness. Defaults to 2.
        font_scale (float, optional): Font scale for text. Defaults to 0.5.
        
    Returns:
        np.ndarray or None: Visualization image if output_path is None, otherwise None.
    """
    # Create blank image if none provided
    if cam_img is None:
        height, width = 800, 1600
        cam_img = np.ones((height, width, 3), dtype=np.uint8) * 255
    else:
        height, width = cam_img.shape[:2]
    
    # Create a copy to avoid modifying the original
    vis_img = cam_img.copy()
    
    # Make sure camera_intrinsics is a 3x4 or 4x4 matrix
    if camera_intrinsics.shape == (3, 3):
        K = np.eye(4)
        K[:3, :3] = camera_intrinsics
        camera_intrinsics = K
    
    # Use standard colors if not provided
    if lane_colors is None:
        lane_colors = {
            0: (255, 255, 255),  # white
            1: (0, 255, 255),    # yellow
            2: (255, 0, 0),      # blue
            3: (0, 0, 255),      # red
            4: (0, 255, 0),      # green
            5: (128, 0, 128),    # purple
            6: (0, 165, 255),    # orange
            7: (255, 255, 0),    # cyan
            8: (255, 0, 255),    # magenta
            9: (0, 255, 128),    # lime
            10: (203, 192, 255), # pink
            11: (128, 128, 0),   # teal
            12: (216, 191, 216), # lavender
            13: (42, 42, 165),   # brown
        }
    
    # Project each lane into camera view
    for lane, lane_type, score in zip(lanes, lane_types, lane_scores):
        if score < show_score_thr:
            continue
            
        # Convert to numpy array if needed
        lane = np.array(lane)
        if len(lane) == 0 or lane.shape[0] < 2:  # Need at least two points
            continue
            
        # Add homogeneous coordinate (1) to lane points
        lane_homo = np.concatenate([lane, np.ones((lane.shape[0], 1))], axis=1)
        
        # Transform from lidar to camera
        lane_cam = lane_homo @ lidar2camera.T
        
        # Filter points in front of camera
        valid_mask = lane_cam[:, 2] > 0
        lane_cam = lane_cam[valid_mask]
        
        if len(lane_cam) < 2:  # Need at least two points
            continue
            
        # Project to image plane using camera intrinsics
        lane_cam_homo = np.concatenate([lane_cam[:, :3], np.ones((lane_cam.shape[0], 1))], axis=1)
        lane_img = lane_cam_homo @ camera_intrinsics.T
        
        # Normalize by z
        lane_img = lane_img[:, :2] / lane_img[:, 2:3]
        
        # Convert to integer pixel coordinates
        lane_img = lane_img.astype(np.int32)
        
        # Filter points within image bounds
        valid_mask = (lane_img[:, 0] >= 0) & (lane_img[:, 0] < width) & \
                     (lane_img[:, 1] >= 0) & (lane_img[:, 1] < height)
        lane_img = lane_img[valid_mask]
        
        if len(lane_img) < 2:  # Need at least two points
            continue
            
        # Get lane type name if lane_classes is provided
        if lane_classes and lane_type < len(lane_classes):
            type_name = lane_classes[lane_type]
        else:
            type_name = f"Type {lane_type}"
        
        # Get lane color
        color = lane_colors.get(lane_type, (200, 200, 200))
        
        # Draw lane line
        for i in range(len(lane_img) - 1):
            pt1 = (lane_img[i, 0], lane_img[i, 1])
            pt2 = (lane_img[i+1, 0], lane_img[i+1, 1])
            cv2.line(vis_img, pt1, pt2, color, thickness) 
        
        # Add label
        label = f"{type_name}: {score:.2f}"
        cv2.putText(vis_img, label, 
                   (lane_img[0, 0], lane_img[0, 1] - 10),
                   cv2.FONT_HERSHEY_SIMPLEX, font_scale, color, thickness)
    
    # Save or return the image
    if output_path:
        os.makedirs(osp.dirname(output_path), exist_ok=True)
        cv2.imwrite(output_path, vis_img)
        return None
    else:
        return vis_img


def visualize_3d_lanes(
    lanes, 
    lane_types, 
    lane_scores, 
    output_path=None, 
    point_cloud_range=None, 
    show_score_thr=0.3,
    lane_colors=None,
    lane_classes=None,
    elev=20, 
    azim=-70,
    figsize=(12, 10)
):
    """Visualize lanes in 3D.
    
    Args:
        lanes (list): List of lane points in 3D.
        lane_types (list): List of lane types.
        lane_scores (list): List of lane confidence scores.
        output_path (str, optional): Path to save visualization. If None, figure is returned.
        point_cloud_range (list, optional): Range of point cloud. 
            Defaults to [0.0, -48.0, -1.0, 97.6, 48.0, 3.0].
        show_score_thr (float, optional): Threshold for showing lanes. Defaults to 0.3.
        lane_colors (dict, optional): Custom colors for lane types. Defaults to None.
        lane_classes (list, optional): Names of lane classes. Defaults to None.
        elev (float, optional): Elevation angle for 3D view. Defaults to 20.
        azim (float, optional): Azimuth angle for 3D view. Defaults to -70.
        figsize (tuple, optional): Figure size (width, height). Defaults to (12, 10).
        
    Returns:
        np.ndarray or None: Visualization image if output_path is None, otherwise None.
    """
    if point_cloud_range is None:
        point_cloud_range = [0.0, -48.0, -1.0, 97.6, 48.0, 3.0]
    
    # Extract point cloud range for visualization
    x_min, y_min, z_min, x_max, y_max, z_max = point_cloud_range
    
    # Create figure and 3D axis
    fig = plt.figure(figsize=figsize)
    ax = fig.add_subplot(111, projection='3d')
    
    # Set axis limits
    ax.set_xlim(x_min, x_max)
    ax.set_ylim(y_min, y_max)
    ax.set_zlim(z_min, z_max)
    
    # Set labels
    ax.set_xlabel('X (m)')
    ax.set_ylabel('Y (m)')
    ax.set_zlabel('Z (m)')
    ax.set_title('3D Lane Detection')
    
    # Get default color map for lane types if not provided
    if lane_colors is None:
        cmap = cm.get_cmap('tab10')
    
    # Plot each lane
    lanes_by_type = {}
    
    for lane, lane_type, score in zip(lanes, lane_types, lane_scores):
        if score < show_score_thr:
            continue
            
        # Convert to numpy array if needed
        lane = np.array(lane)
        if len(lane) == 0 or lane.shape[0] < 2:  # Need at least two points
            continue
            
        # Extract x, y, z coordinates
        x, y, z = lane[:, 0], lane[:, 1], lane[:, 2]
        
        # Get lane type name if lane_classes is provided
        if lane_classes and lane_type < len(lane_classes):
            type_name = lane_classes[lane_type]
        else:
            type_name = f"Type {lane_type}"
        
        # Get lane color
        if lane_colors is not None and lane_type in lane_colors:
            rgb_color = lane_colors[lane_type]
            # Convert BGR to RGB if needed
            if isinstance(rgb_color, tuple) and len(rgb_color) == 3:
                if rgb_color[0] > 1 or rgb_color[1] > 1 or rgb_color[2] > 1:
                    # Normalize BGR values to [0, 1]
                    normalized_color = (rgb_color[2]/255, rgb_color[1]/255, rgb_color[0]/255)
                else:
                    normalized_color = rgb_color
            else:
                normalized_color = cmap(lane_type % 10)
        else:
            normalized_color = cmap(lane_type % 10)
        
        # Plot lane with color based on lane type
        label = f"{type_name}, score: {score:.2f}"
        
        # Group lanes by type for legend
        if type_name not in lanes_by_type:
            lanes_by_type[type_name] = {'color': normalized_color, 'score': score}
            ax.plot(x, y, z, '-', color=normalized_color, linewidth=2, label=label)
        else:
            ax.plot(x, y, z, '-', color=normalized_color, linewidth=2)
    
    # Add ego vehicle marker
    ax.plot([0], [0], [0], 'ko', markersize=10, label='Ego Vehicle')
    
    # Add legend
    ax.legend(loc='upper right')
    
    # Set the view angle
    ax.view_init(elev=elev, azim=azim)
    
    # Save figure or return the image
    plt.tight_layout()
    
    if output_path:
        os.makedirs(osp.dirname(output_path), exist_ok=True)
        plt.savefig(output_path, dpi=200, bbox_inches='tight')
        plt.close()
        return None
    else:
        # Convert matplotlib figure to numpy array for return
        fig.canvas.draw()
        img = np.array(fig.canvas.renderer.buffer_rgba())
        plt.close()
        return img


def visualize_lane_detection_results(
    results, 
    img=None, 
    camera_intrinsics=None, 
    lidar2camera=None,
    point_cloud_range=None,
    output_dir=None,
    prefix=None,
    lane_classes=None,
    show_score_thr=0.3,
    vis_modes=('bev', '3d', 'camera')
):
    """Visualize lane detection results in different views.
    
    Args:
        results (dict): Lane detection results containing 'pred_lanes', 'pred_lane_types', 'pred_scores'.
        img (np.ndarray, optional): Camera image for visualization. Required for 'camera' mode.
        camera_intrinsics (np.ndarray, optional): Camera intrinsic matrix. Required for 'camera' mode.
        lidar2camera (np.ndarray, optional): LiDAR to camera transformation. Required for 'camera' mode.
        point_cloud_range (list, optional): Range of point cloud. 
        output_dir (str, optional): Output directory for visualizations.
        prefix (str, optional): Prefix for output filenames.
        lane_classes (list, optional): Names of lane classes.
        show_score_thr (float, optional): Threshold for showing lanes. Defaults to 0.3.
        vis_modes (tuple, optional): Visualization modes. Defaults to ('bev', '3d', 'camera').
        
    Returns:
        dict: Dictionary with visualization images if output_dir is None, otherwise None.
    """
    # Extract results
    lanes = results.get('pred_lanes', [])
    lane_types = results.get('pred_lane_types', [0] * len(lanes))
    lane_scores = results.get('pred_scores', [1.0] * len(lanes))
    
    # Ensure output directory exists if provided
    if output_dir:
        os.makedirs(output_dir, exist_ok=True)
        if prefix is None:
            prefix = 'lane_detection'
    
    # Initialize output dictionary for returning images
    visualization_images = {}
    
    # BEV visualization
    if 'bev' in vis_modes:
        bev_output_path = None
        if output_dir:
            bev_output_path = osp.join(output_dir, f"{prefix}_bev.png")
        
        bev_img = visualize_bev_lanes(
            lanes, lane_types, lane_scores, 
            output_path=bev_output_path,
            point_cloud_range=point_cloud_range,
            show_score_thr=show_score_thr,
            lane_classes=lane_classes
        )
        
        if bev_img is not None:
            visualization_images['bev'] = bev_img
    
    # 3D visualization
    if '3d' in vis_modes:
        vis_3d_output_path = None
        if output_dir:
            vis_3d_output_path = osp.join(output_dir, f"{prefix}_3d.png")
        
        vis_3d_img = visualize_3d_lanes(
            lanes, lane_types, lane_scores, 
            output_path=vis_3d_output_path,
            point_cloud_range=point_cloud_range,
            show_score_thr=show_score_thr,
            lane_classes=lane_classes
        )
        
        if vis_3d_img is not None:
            visualization_images['3d'] = vis_3d_img
    
    # Camera view visualization
    if 'camera' in vis_modes and img is not None and camera_intrinsics is not None and lidar2camera is not None:
        cam_output_path = None
        if output_dir:
            cam_output_path = osp.join(output_dir, f"{prefix}_camera.png")
        
        cam_img = visualize_camera_lanes(
            lanes, lane_types, lane_scores,
            camera_intrinsics=camera_intrinsics,
            lidar2camera=lidar2camera,
            cam_img=img,
            output_path=cam_output_path,
            show_score_thr=show_score_thr,
            lane_classes=lane_classes
        )
        
        if cam_img is not None:
            visualization_images['camera'] = cam_img
    
    return visualization_images if not output_dir else None


def main():
    """Example usage when running the script directly."""
    import argparse
    
    parser = argparse.ArgumentParser(description='Visualize 3D lane detection results')
    parser.add_argument('--results', type=str, required=True, help='Path to the lane detection results (JSON file or directory)')
    parser.add_argument('--img-dir', type=str, help='Directory containing camera images')
    parser.add_argument('--calib-file', type=str, help='Path to calibration file')
    parser.add_argument('--output-dir', type=str, default='vis_results', help='Output directory for visualizations')
    parser.add_argument('--show-score-thr', type=float, default=0.3, help='Score threshold for visualization')
    parser.add_argument('--vis-mode', type=str, choices=['bev', '3d', 'camera', 'all'], default='all', 
                        help='Visualization mode')
    parser.add_argument('--point-cloud-range', type=float, nargs=6, 
                        default=[0.0, -48.0, -1.0, 97.6, 48.0, 3.0],
                        help='Point cloud range [x_min, y_min, z_min, x_max, y_max, z_max]')
    parser.add_argument('--lane-classes-file', type=str, help='Path to lane classes file')
    args = parser.parse_args()
    
    # Determine visualization modes
    vis_modes = ['bev', '3d', 'camera'] if args.vis_mode == 'all' else [args.vis_mode]
    
    # Load lane classes if provided
    lane_classes = None
    if args.lane_classes_file and osp.exists(args.lane_classes_file):
        with open(args.lane_classes_file, 'r') as f:
            lane_classes = [line.strip() for line in f.readlines()]
    
    # Load calibration if provided
    camera_intrinsics = None
    lidar2camera = None
    if args.calib_file and osp.exists(args.calib_file):
        with open(args.calib_file, 'r') as f:
            calib_data = json.load(f)
        
        # Assuming specific format, adjust as needed
        if '120_front' in calib_data:
            cam_data = calib_data['120_front']
            intrinsic = np.array(cam_data['intrinsic']).reshape(3, 3)
            camera_intrinsics = np.eye(4).astype(np.float32)
            camera_intrinsics[:3, :3] = intrinsic
            
            extrinsic = np.array(cam_data['extrinsic']).reshape(3, 4)
            lidar2camera = np.eye(4).astype(np.float32)
            lidar2camera[:3, :] = extrinsic
    
    # Process results
    if osp.isdir(args.results):
        # Process all JSON files in the directory
        for filename in os.listdir(args.results):
            if filename.endswith('.json'):
                json_path = osp.join(args.results, filename)
                with open(json_path, 'r') as f:
                    result_data = json.load(f)
                
                # Extract frame ID
                frame_id = result_data.get('frame_id', osp.splitext(filename)[0])
                
                # Find corresponding image if image directory is provided
                img = None
                if args.img_dir and 'camera' in vis_modes:
                    img_path = osp.join(args.img_dir, '120_front', f"{frame_id}.jpg")
                    if osp.exists(img_path):
                        img = cv2.imread(img_path)
                        img = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)
                
                # Prepare results format
                lanes = []
                lane_types = []
                lane_scores = []
                
                for lane in result_data.get('lanes', []):
                    lanes.append(lane.get('points_3d', []))
                    lane_types.append(lane.get('type_id', 0))
                    lane_scores.append(lane.get('score', 1.0))
                
                results = {
                    'pred_lanes': lanes,
                    'pred_lane_types': lane_types,
                    'pred_scores': lane_scores
                }
                
                # Visualize results
                output_dir = osp.join(args.output_dir, frame_id)
                visualize_lane_detection_results(
                    results,
                    img=img,
                    camera_intrinsics=camera_intrinsics,
                    lidar2camera=lidar2camera,
                    point_cloud_range=args.point_cloud_range,
                    output_dir=output_dir,
                    prefix='lane_detection',
                    lane_classes=lane_classes,
                    show_score_thr=args.show_score_thr,
                    vis_modes=vis_modes
                )
                
                print(f"Visualized results for frame {frame_id}")
    else:
        # Process single JSON file
        with open(args.results, 'r') as f:
            result_data = json.load(f)
        
        # Extract frame ID
        frame_id = result_data.get('frame_id', osp.splitext(osp.basename(args.results))[0])
        
        # Find corresponding image if image directory is provided
        img = None
        if args.img_dir and 'camera' in vis_modes:
            img_path = osp.join(args.img_dir, '120_front', f"{frame_id}.jpg")
            if osp.exists(img_path):
                img = cv2.imread(img_path)
                img = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)
        
        # Prepare results format
        lanes = []
        lane_types = []
        lane_scores = []
        
        for lane in result_data.get('lanes', []):
            lanes.append(lane.get('points_3d', []))
            lane_types.append(lane.get('type_id', 0))
            lane_scores.append(lane.get('score', 1.0))
        
        results = {
            'pred_lanes': lanes,
            'pred_lane_types': lane_types,
            'pred_scores': lane_scores
        }
        
        # Visualize results
        output_dir = osp.join(args.output_dir, frame_id)
        visualize_lane_detection_results(
            results,
            img=img,
            camera_intrinsics=camera_intrinsics,
            lidar2camera=lidar2camera,
            point_cloud_range=args.point_cloud_range,
            output_dir=output_dir,
            prefix='lane_detection',
            lane_classes=lane_classes,
            show_score_thr=args.show_score_thr,
            vis_modes=vis_modes
        )
        
        print(f"Visualized results for frame {frame_id}")


if __name__ == '__main__':
    main() 
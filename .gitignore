# Byte-compiled / optimized / DLL files
__pycache__/
*.py[cod]
*$py.class

# C extensions
*.so

# Distribution / packaging
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
pip-wheel-metadata/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST
data
pretrained
# *.json
*.pth
*.out
event*
viz*
eval.sh
# PyInstaller
#  Usually these files are written by a python script from a template
#  before PyInstaller builds the exe, so as to inject date/other infos into it.
*.manifest
*.spec

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Unit test / coverage reports
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.py,cover
.hypothesis/
.pytest_cache/

# Translations
*.mo
*.pot

# Django stuff:
*.log
local_settings.py
db.sqlite3
db.sqlite3-journal

# Flask stuff:
instance/
.webassets-cache

# Scrapy stuff:
.scrapy

# Sphinx documentation
docs/_build/

# PyBuilder
target/

# Jupyter Notebook
.ipynb_checkpoints

# IPython
profile_default/
ipython_config.py

# pyenv
.python-version

# pipenv
#   According to pypa/pipenv#598, it is recommended to include Pipfile.lock in version control.
#   However, in case of collaboration, if having platform-specific dependencies or dependencies
#   having no cross-platform support, pipenv may install dependencies that don't work, or not
#   install all needed dependencies.
#Pipfile.lock

# PEP 582; used by e.g. github.com/David-OConnor/pyflow
__pypackages__/

# Celery stuff
celerybeat-schedule
celerybeat.pid

# SageMath parsed files
*.sage.py

# Environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# Spyder project settings
.spyderproject
.spyproject

# Rope project settings
.ropeproject

# mkdocs documentation
/site

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# Pyre type checker
.pyre/

# TODO: debug files
*.txt
*.jpg
runs/**
data_ori
7000gt_viz/**
vis_v1_aug/**
vis_v1_aug_error/**
visres/**
bevfusion_v1_gpu8_falcon7000_smallrange_e60_on_train_viz/**
bevfusion_v1_gpu8_falcon7000_smallrange_e60_on_train_viz_VS_7000gt_viz/**

res/**
results.pkl
dump
output*/
runs
core.*
results/
.vscode/
experiments/
lidar_debug_vis/
train_vis_undist/
at128_second1231_min64_cat256_3dseg_cls21_cam5lidar_metric/

# YoYo AI version control directory
.yoyo/
debug_*.py
dump-data.py
dump_data_B2.py
project_demo/bev_lane_det/models/
work_dirs/

# Additional files to ignore
COMPREHENSIVE_ANALYSIS_REPORT.md
README_3D_LANE_DETECTION.md
SIMPLIFIED_CONFIG_SUMMARY.md
final_review_gate.py
repomix-output.md
test_interpolation_fix.py
test_model_forward.py
test_simplified_config.py
validate_lane_detection_setup.py
verify_calibration.py
mmdet3d/datasets/calibrated_sensor.json
mmdet3d/datasets/calibration.json
mmdet3d/datasets/segment_1_label.json
